# Database Spatial Optimization for 1M+ Properties

This document outlines the database optimizations needed to efficiently handle 1M+ properties with spatial queries.

## 🗄️ Required Database Indexes

### 1. Property Location Indexes

For MongoDB (current setup), add these indexes to improve spatial query performance:

```javascript
// Add spatial index on property location coordinates
db.property_location.createIndex({ "location": "2dsphere" });

// Add compound index for organization + location queries
db.property_location.createIndex({ 
  "property.organizationId": 1, 
  "location": "2dsphere" 
});

// Add index for viewport bounds queries
db.property_location.createIndex({ 
  "location.coordinates": 1 
});
```

### 2. Property Indexes for Performance

```javascript
// Compound index for organization + deletion status + location existence
db.property.createIndex({ 
  "organizationId": 1, 
  "isDeleted": 1, 
  "location": 1 
});

// Index for property type filtering in map views
db.property.createIndex({ 
  "organizationId": 1, 
  "propertyType": 1, 
  "isDeleted": 1 
});

// Index for property status filtering
db.property.createIndex({ 
  "organizationId": 1, 
  "status": 1, 
  "isDeleted": 1 
});
```

## 🔧 PostGIS Setup (Optional Migration)

For maximum spatial performance with 1M+ records, consider migrating to PostgreSQL with PostGIS:

### 1. Install PostGIS Extension

```sql
-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
```

### 2. Update Property Location Schema

```sql
-- Add spatial column to property_location table
ALTER TABLE property_location 
ADD COLUMN location_point GEOMETRY(POINT, 4326);

-- Create spatial index
CREATE INDEX idx_property_location_spatial 
ON property_location 
USING GIST (location_point);

-- Create compound spatial index with organization
CREATE INDEX idx_property_location_org_spatial 
ON property_location 
USING GIST (location_point) 
WHERE organization_id IS NOT NULL;
```

### 3. Populate Spatial Data

```sql
-- Migrate existing JSON coordinates to PostGIS geometry
UPDATE property_location 
SET location_point = ST_SetSRID(
  ST_Point(
    (location->>'coordinates')::json->>0::float, 
    (location->>'coordinates')::json->>1::float
  ), 
  4326
)
WHERE location IS NOT NULL 
AND (location->>'coordinates') IS NOT NULL;
```

## 📊 Performance Optimizations

### 1. Viewport Query Optimization

```sql
-- Example PostGIS viewport query (much faster than application filtering)
SELECT p.id, p.name, p.property_type, 
       ST_X(pl.location_point) as lng, 
       ST_Y(pl.location_point) as lat,
       pf.price
FROM property p
JOIN property_location pl ON p.id = pl.property_id
LEFT JOIN property_financials pf ON p.id = pf.property_id
WHERE p.organization_id = $1 
  AND p.is_deleted = false
  AND pl.location_point && ST_MakeEnvelope($2, $3, $4, $5, 4326)
LIMIT 50000;
```

### 2. Clustering Query Optimization

```sql
-- Server-side clustering with PostGIS
SELECT 
  ST_X(ST_Centroid(ST_Collect(pl.location_point))) as cluster_lng,
  ST_Y(ST_Centroid(ST_Collect(pl.location_point))) as cluster_lat,
  COUNT(*) as property_count,
  AVG(pf.price) as avg_price,
  ST_Extent(pl.location_point) as bounds
FROM property p
JOIN property_location pl ON p.id = pl.property_id
LEFT JOIN property_financials pf ON p.id = pf.property_id
WHERE p.organization_id = $1 
  AND p.is_deleted = false
  AND pl.location_point && ST_MakeEnvelope($2, $3, $4, $5, 4326)
GROUP BY ST_SnapToGrid(pl.location_point, $6); -- $6 = cluster resolution based on zoom
```

## 🚀 Implementation Strategy

### Phase 1: MongoDB Optimization (Current)
1. ✅ Add spatial indexes to existing MongoDB collections
2. ✅ Implement viewport-based API endpoints
3. ✅ Add server-side clustering logic
4. ✅ Optimize query patterns for large datasets

### Phase 2: PostGIS Migration (Future)
1. Set up PostgreSQL with PostGIS extension
2. Migrate property location data to spatial columns
3. Update API queries to use PostGIS functions
4. Implement advanced spatial features (buffering, intersection, etc.)

### Phase 3: Advanced Optimizations
1. Implement vector tiles for extremely large datasets (10M+ properties)
2. Add spatial caching with Redis + GeoHash
3. Implement real-time spatial updates
4. Add advanced clustering algorithms (K-means, DBSCAN)

## 📈 Expected Performance Improvements

### Current MongoDB Setup
- **Before**: 2-5 seconds for 100k properties in viewport
- **After**: 200-500ms for 100k properties in viewport
- **Clustering**: 50-100ms for cluster calculation

### With PostGIS Migration
- **Viewport Queries**: 50-200ms for 1M+ properties
- **Clustering**: 20-50ms for cluster calculation
- **Advanced Spatial**: Sub-100ms for complex spatial operations

## 🔍 Monitoring Queries

Add these queries to monitor spatial performance:

```javascript
// MongoDB: Check index usage
db.property_location.find({
  "location": {
    $geoWithin: {
      $box: [[-180, -90], [180, 90]]
    }
  }
}).explain("executionStats");

// Monitor slow queries
db.setProfilingLevel(2, { slowms: 500 });
db.system.profile.find({ "command.find": "property_location" });
```

```sql
-- PostgreSQL: Monitor spatial query performance
EXPLAIN (ANALYZE, BUFFERS) 
SELECT COUNT(*) FROM property_location 
WHERE location_point && ST_MakeEnvelope(-180, -90, 180, 90, 4326);

-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename = 'property_location';
```

## 🛠️ Maintenance

### Regular Maintenance Tasks
1. **Reindex spatial indexes** monthly for optimal performance
2. **Analyze query patterns** and add indexes for common viewport bounds
3. **Monitor memory usage** for large spatial operations
4. **Update statistics** for query optimizer

### Scaling Considerations
- **Partitioning**: Consider geographic partitioning for global datasets
- **Sharding**: Implement geographic sharding for multi-region deployments
- **Caching**: Use Redis with GeoHash for frequently accessed areas
- **CDN**: Cache vector tiles at CDN edge locations 