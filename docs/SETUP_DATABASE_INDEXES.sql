-- Database indexes for optimizing map performance with large property datasets
-- Run these indexes to improve spatial query performance

-- For MongoDB (if using MongoDB as the database)
-- Run in MongoDB shell:
/*
// Create spatial index on property locations (GeoJSON)
db.property_location.createIndex({ "location": "2dsphere" });

// Create compound index for organization + location queries
db.property_location.createIndex({ 
  "property.organizationId": 1, 
  "location": "2dsphere" 
});

// Create indexes for common property queries
db.property.createIndex({ 
  "organizationId": 1, 
  "isDeleted": 1, 
  "location": 1 
});

// Index for property type filtering
db.property.createIndex({ 
  "organizationId": 1, 
  "propertyType": 1, 
  "isDeleted": 1 
});
*/

-- For PostgreSQL with PostGIS (future optimization)
-- Uncomment and run these if migrating to PostgreSQL:

/*
-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;

-- Add spatial column if it doesn't exist
-- ALTER TABLE property_location ADD COLUMN location_point GEOMETRY(POINT, 4326);

-- Create spatial index
CREATE INDEX CONCURRENTLY idx_property_location_spatial 
ON property_location USING GIST (location_point);

-- Create compound spatial index with organization
CREATE INDEX CONCURRENTLY idx_property_location_org_spatial 
ON property_location USING GIST (location_point) 
WHERE organization_id IS NOT NULL;

-- Create indexes for common property queries
CREATE INDEX CONCURRENTLY idx_property_org_deleted 
ON property (organization_id, is_deleted) 
WHERE is_deleted = false;

-- Index for property type filtering
CREATE INDEX CONCURRENTLY idx_property_type_org 
ON property (organization_id, property_type, is_deleted) 
WHERE is_deleted = false;

-- Index for location existence check
CREATE INDEX CONCURRENTLY idx_property_has_location 
ON property (organization_id, is_deleted) 
WHERE location IS NOT NULL AND is_deleted = false;
*/

-- Current Priority (for immediate performance improvement):
-- 1. Add database connection pooling
-- 2. Increase Prisma query timeout
-- 3. Add basic indexes on frequently queried columns
-- 4. Implement query result caching

-- Performance Monitoring Queries:
-- Use these to monitor query performance and identify slow operations

/*
-- MongoDB: Check index usage
db.property.explain("executionStats").find({
  "organizationId": "your-org-id",
  "isDeleted": false,
  "location": { $ne: null }
});

-- PostgreSQL: Check query performance
EXPLAIN (ANALYZE, BUFFERS) 
SELECT COUNT(*) FROM property 
WHERE organization_id = 'your-org-id' 
AND is_deleted = false 
AND location IS NOT NULL;
*/ 