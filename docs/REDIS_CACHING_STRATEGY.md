# Redis Caching Strategy for 1M+ Properties

This document outlines the Redis caching implementation for optimizing viewport-based property queries and clustering operations.

## 🚀 Overview

The caching strategy uses Redis to store pre-computed clusters and viewport data, reducing database load and improving response times for map operations with large datasets.

## 📋 Cache Structure

### 1. Viewport Cache Keys

```typescript
// Cache key format for viewport queries
const VIEWPORT_CACHE_KEY = `viewport:${organizationId}:${north}:${south}:${east}:${west}:${zoom}`;

// Example: viewport:org123:40.7829:-73.9441:40.7489:-74.0441:12
```

### 2. Cluster Cache Keys

```typescript
// Cache key format for pre-computed clusters
const CLUSTER_CACHE_KEY = `clusters:${organizationId}:${zoom}:${gridHash}`;

// Example: clusters:org123:10:9q8yy
```

### 3. Property Count Cache

```typescript
// Cache organization-wide property counts
const PROPERTY_COUNT_KEY = `property_count:${organizationId}`;
```

## 🔧 Implementation

### 1. Enhanced Viewport API with Redis

Update the viewport API to use Redis caching:

```typescript
// packages/api/src/routes/properties/router.ts

import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// Cache TTL configuration
const CACHE_TTL = {
  VIEWPORT: 300, // 5 minutes for viewport data
  CLUSTERS: 1800, // 30 minutes for cluster data
  COUNTS: 3600, // 1 hour for property counts
};

// Enhanced viewport endpoint with caching
propertiesRouter.post("/viewport", authMiddleware, async (c) => {
  try {
    const { organizationId, north, south, east, west, zoom, enableClustering, limit } = 
      ViewportQuerySchema.parse(await c.req.json());

    // Generate cache key
    const cacheKey = `viewport:${organizationId}:${north.toFixed(4)}:${south.toFixed(4)}:${east.toFixed(4)}:${west.toFixed(4)}:${Math.floor(zoom)}`;
    
    // Try to get from cache first
    const cached = await redis.get(cacheKey);
    if (cached) {
      logger.info(`Cache hit for viewport query: ${cacheKey}`);
      return c.json(JSON.parse(cached));
    }

    // If not in cache, compute and store
    const result = await computeViewportData(organizationId, bounds, zoom, enableClustering, limit);
    
    // Cache the result
    await redis.setex(cacheKey, CACHE_TTL.VIEWPORT, JSON.stringify(result));
    
    logger.info(`Cache miss - stored viewport query: ${cacheKey}`);
    return c.json(result);
    
  } catch (error) {
    logger.error("Viewport query failed:", error);
    return c.json({ error: "Failed to fetch properties" }, 500);
  }
});
```

### 2. Cluster Pre-computation

Implement background cluster pre-computation:

```typescript
// Background job to pre-compute clusters
export async function precomputeClusters(organizationId: string) {
  const zoomLevels = [8, 9, 10, 11, 12, 13]; // Pre-compute for common zoom levels
  
  for (const zoom of zoomLevels) {
    // Get all properties for organization
    const properties = await db.property.findMany({
      where: { organizationId, isDeleted: false },
      include: { location: { select: { location: true } } }
    });

    // Generate clusters for this zoom level
    const clusters = clusterProperties(properties, zoom);
    
    // Store in cache with geographic grid hash
    const gridSize = getGridSize(zoom);
    const grids = partitionIntoGrids(clusters, gridSize);
    
    for (const [gridHash, gridClusters] of grids) {
      const cacheKey = `clusters:${organizationId}:${zoom}:${gridHash}`;
      await redis.setex(cacheKey, CACHE_TTL.CLUSTERS, JSON.stringify(gridClusters));
    }
  }
  
  logger.info(`Pre-computed clusters for organization ${organizationId}`);
}
```

### 3. Geographic Grid Partitioning

```typescript
// Partition clusters into geographic grids for efficient caching
function partitionIntoGrids(clusters: ClusterResult[], gridSize: number): Map<string, ClusterResult[]> {
  const grids = new Map<string, ClusterResult[]>();
  
  for (const cluster of clusters) {
    const [lng, lat] = cluster.position;
    const gridHash = encodeGridHash(lng, lat, gridSize);
    
    if (!grids.has(gridHash)) {
      grids.set(gridHash, []);
    }
    grids.get(gridHash)!.push(cluster);
  }
  
  return grids;
}

function encodeGridHash(lng: number, lat: number, gridSize: number): string {
  const gridLng = Math.floor(lng / gridSize) * gridSize;
  const gridLat = Math.floor(lat / gridSize) * gridSize;
  return `${gridLng.toFixed(4)}_${gridLat.toFixed(4)}`;
}

function getGridSize(zoom: number): number {
  // Adaptive grid size based on zoom level
  return Math.max(0.001, 0.1 / Math.pow(2, zoom - 8));
}
```

## 📊 Cache Invalidation Strategy

### 1. Time-based Invalidation

```typescript
// TTL-based invalidation for different data types
const CACHE_TTL = {
  VIEWPORT: 300,      // 5 minutes - frequently changing viewport data
  CLUSTERS: 1800,     // 30 minutes - relatively stable cluster data
  COUNTS: 3600,       // 1 hour - property counts change less frequently
  METADATA: 7200,     // 2 hours - organization metadata
};
```

### 2. Event-based Invalidation

```typescript
// Invalidate cache when properties are added/updated/deleted
export async function invalidatePropertyCache(organizationId: string, propertyId?: string) {
  const patterns = [
    `viewport:${organizationId}:*`,
    `clusters:${organizationId}:*`,
    `property_count:${organizationId}`,
  ];
  
  for (const pattern of patterns) {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
      logger.info(`Invalidated ${keys.length} cache keys for pattern: ${pattern}`);
    }
  }
}

// Hook into property mutations
export async function onPropertyChange(organizationId: string, changeType: 'create' | 'update' | 'delete') {
  await invalidatePropertyCache(organizationId);
  
  // Trigger cluster pre-computation for popular zoom levels
  if (changeType === 'create') {
    // Schedule background cluster recomputation
    scheduleClusterRecomputation(organizationId);
  }
}
```

### 3. Smart Cache Warming

```typescript
// Cache warming for popular viewports
export async function warmPopularViewports(organizationId: string) {
  // Common viewport patterns (major cities, popular areas)
  const popularViewports = [
    { bounds: { north: 40.9176, south: 40.4774, east: -73.7004, west: -74.2591 }, zoom: 10 }, // NYC
    { bounds: { north: 34.3373, south: 33.7037, east: -118.1553, west: -118.6681 }, zoom: 10 }, // LA
    // Add more popular areas based on user analytics
  ];
  
  for (const viewport of popularViewports) {
    const cacheKey = `viewport:${organizationId}:${viewport.bounds.north.toFixed(4)}:${viewport.bounds.south.toFixed(4)}:${viewport.bounds.east.toFixed(4)}:${viewport.bounds.west.toFixed(4)}:${viewport.zoom}`;
    
    const exists = await redis.exists(cacheKey);
    if (!exists) {
      // Pre-compute and cache popular viewport
      const result = await computeViewportData(organizationId, viewport.bounds, viewport.zoom, true, 10000);
      await redis.setex(cacheKey, CACHE_TTL.VIEWPORT, JSON.stringify(result));
    }
  }
}
```

## 🔍 Performance Monitoring

### 1. Cache Hit Ratio Tracking

```typescript
// Track cache performance metrics
const cacheMetrics = {
  hits: 0,
  misses: 0,
  errors: 0,
};

// Middleware to track cache performance
export function trackCacheMetrics(operation: 'hit' | 'miss' | 'error') {
  cacheMetrics[operation]++;
  
  // Log metrics every 100 operations
  const total = cacheMetrics.hits + cacheMetrics.misses;
  if (total % 100 === 0) {
    const hitRatio = (cacheMetrics.hits / total) * 100;
    logger.info(`Cache hit ratio: ${hitRatio.toFixed(2)}% (${cacheMetrics.hits}/${total})`);
  }
}
```

### 2. Cache Size Monitoring

```typescript
// Monitor Redis memory usage
export async function getCacheStats() {
  const info = await redis.info('memory');
  const memoryUsage = parseRedisInfo(info);
  
  return {
    memoryUsed: memoryUsage.used_memory_human,
    memoryPeak: memoryUsage.used_memory_peak_human,
    keyCount: await redis.dbsize(),
    hitRatio: (cacheMetrics.hits / (cacheMetrics.hits + cacheMetrics.misses)) * 100,
  };
}
```

## 🚀 Deployment Configuration

### 1. Redis Configuration

```bash
# Redis configuration for optimal performance
# redis.conf

# Memory management
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence (optional for cache-only usage)
save ""
appendonly no

# Performance tuning
tcp-keepalive 60
timeout 300

# Compression
rdbcompression yes
```

### 2. Environment Variables

```bash
# .env configuration
REDIS_URL=redis://localhost:6379
REDIS_CACHE_ENABLED=true
REDIS_CACHE_TTL_VIEWPORT=300
REDIS_CACHE_TTL_CLUSTERS=1800
REDIS_CACHE_TTL_COUNTS=3600

# Cache warming settings
CACHE_WARM_ON_STARTUP=true
CACHE_PRECOMPUTE_CLUSTERS=true
```

### 3. Monitoring and Alerts

```typescript
// Health check endpoint
propertiesRouter.get("/cache-health", authMiddleware, async (c) => {
  try {
    const stats = await getCacheStats();
    const redisInfo = await redis.ping();
    
    return c.json({
      status: redisInfo === 'PONG' ? 'healthy' : 'unhealthy',
      stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return c.json({ status: 'error', error: error.message }, 500);
  }
});
```

## 📈 Expected Performance Improvements

### With Redis Caching

- **Cache Hit Scenarios**: 50-100ms response time (vs 500-2000ms without cache)
- **Popular Viewports**: Near-instant loading for frequently accessed areas
- **Cluster Operations**: 80% reduction in database load
- **Memory Usage**: Efficient memory utilization with LRU eviction
- **Scalability**: Supports 10M+ properties with sub-second response times

### Cache Hit Ratio Targets

- **Viewport Queries**: 70-80% hit ratio (users often pan around same areas)
- **Cluster Data**: 85-95% hit ratio (clusters change less frequently)
- **Property Counts**: 95%+ hit ratio (counts update infrequently)

This caching strategy provides a robust foundation for handling 1M+ properties with optimal performance and minimal database load. 