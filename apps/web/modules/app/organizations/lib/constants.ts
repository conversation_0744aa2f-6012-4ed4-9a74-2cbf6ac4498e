import { IconBuilding, IconHome, IconMapPin2, IconUsers } from "@tabler/icons-react";
import { cn } from "@ui/lib";
import type {
	DataTableFilter<PERSON>ield,
	SheetField,
} from "../../object-views/lib/types";
import type { Company } from "../components/objects/companies/schema";
import type { Contact } from "../components/objects/contacts/schema";
import type { Property } from "../components/objects/properties/schema";
import { PROPERTY_STATUSES, PROPERTY_TYPES } from "../components/objects/properties/schema";
import { CONTACT_PERSONA, CONTACT_STAGE, CONTACT_STATUS } from "@app/shared/lib/constants";

// Address fields for consistent usage across components
export const ADDRESS_FIELDS = {
	STREET: "street",
	STREET2: "street2",
	CITY: "city",
	STATE: "state",
	ZIP: "zip",
	COUNTY: "county",
	COUNTRY: "country",
} as const;

export const ADDRESS_LABELS = {
	[ADDRESS_FIELDS.STREET]: "Street Address",
	[ADDRESS_FIELDS.STREET2]: "Street Address 2",
	[ADDRESS_FIELDS.CITY]: "City",
	[ADDRESS_FIELDS.STATE]: "State",
	[ADDRESS_FIELDS.ZIP]: "ZIP Code",
	[ADDRESS_FIELDS.COUNTY]: "County",
	[ADDRESS_FIELDS.COUNTRY]: "Country",
} as const;

// TODO: FIXME: Maybe this should be in the shared folder and we should move the separate
// object constants to the respective object folders

export const objectItems = [
	{
		title: "Contacts",
		url: "contacts",
		icon: IconUsers,
		description: "Manage your contacts and relationships",
		className: "bg-blue-500 p-1 rounded-md",
		objectType: "contact",
	},
	{
		title: "Properties",
		url: "properties",
		icon: IconHome,
		description: "Manage your property listings",
		className: "bg-orange-500 p-1 rounded-md",
		objectType: "property",
		viewType: "map",
		viewIcon: IconMapPin2,
		viewTitle: "Map",
		viewColor: "bg-red-500 p-0.5 !rounded-sm",
	},
	{
		title: "Companies",
		url: "companies",
		icon: IconBuilding,
		description: "Manage company information",
		className: "bg-purple-500 p-1 rounded-md",
		objectType: "company",
	},
];

export const contactFilterFields: DataTableFilterField<Contact>[] = [
	{
		label: "Record ID",
		value: "id",
		type: "input",
	},
	{
		label: "Created",
		value: "createdAt",
		type: "timerange",
		defaultOpen: true,
		commandDisabled: true,
	},
	{
		label: "First Name",
		value: "firstName",
		type: "input",
	},
	{
		label: "Last Name",
		value: "lastName",
		type: "input",
	},
	{
		label: "Status",
		value: "status",
		type: "checkbox",
		defaultOpen: true,
		options: CONTACT_STATUS.map((status) => ({
			label: status.label,
			value: status.value,
		})),
	},
	{
		label: "Persona",
		value: "persona",
		type: "checkbox",
		options: CONTACT_PERSONA.map((persona) => ({
			label: persona.label,
			value: persona.value,
		})),
	},
	{
		label: "Stage",
		value: "stage",
		type: "checkbox",
		options: CONTACT_STAGE.map((stage) => ({
			label: stage.label,
			value: stage.value,
		})),
	},
	{
		label: "Name",
		value: "name",
		type: "input",
	},
	{
		label: "Email",
		value: "email",
		type: "input",
	},
	{
		label: "Phone",
		value: "phone",
		type: "input",
	},
	{
		label: "Title",
		value: "title",
		type: "input",
	},
	{
		label: "Company",
		value: "company",
		type: "input",
	},
	{
		label: "Tags",
		value: "tags",
		type: "checkbox",
		defaultOpen: false,
		commandDisabled: false,
		options: [],
	},
];

export const contactSheetFields: SheetField<Contact, any>[] = [
	{
		id: "id",
		label: "Contact ID",
		type: "readonly",
		skeletonClassName: "w-64",
	},
	{
		id: "createdAt",
		label: "Created",
		type: "timerange",
		skeletonClassName: "w-36",
	},
	{
		id: "name",
		label: "Name",
		type: "input",
		skeletonClassName: "w-48",
	},
	{
		id: "email",
		label: "Email",
		type: "input",
		skeletonClassName: "w-56",
	},
	{
		id: "phone",
		label: "Phone",
		type: "input",
		skeletonClassName: "w-32",
	},
	{
		id: "status",
		label: "Status",
		type: "checkbox",
		skeletonClassName: "w-16",
	},
	{
		id: "persona",
		label: "Persona",
		type: "checkbox",
		skeletonClassName: "w-16",
	},
	{
		id: "title",
		label: "Title",
		type: "input",
		skeletonClassName: "w-32",
	},
	{
		id: "company",
		label: "Company",
		type: "input",
		skeletonClassName: "w-40",
	},
	{
		id: "website",
		label: "Website",
		type: "input",
		skeletonClassName: "w-full",
	},
	{
		id: "summary",
		label: "Summary",
		type: "input",
		skeletonClassName: "w-full",
	},
];

export const companyFilterFields: DataTableFilterField<Company>[] = [
	{
		label: "Created",
		value: "createdAt",
		type: "timerange",
		defaultOpen: true,
		commandDisabled: false,
	},
	{
		label: "Industry",
		value: "industry",
		type: "input",
	},
	{
		label: "Company Size",
		value: "size",
		type: "input",
	},
	{
		label: "Company Name",
		value: "name",
		type: "input",
	},
	{
		label: "Website",
		value: "website",
		type: "input",
	},
	{
		label: "Email",
		value: "email",
		type: "input",
	},
	{
		label: "Phone",
		value: "phone",
		type: "input",
	},
	{
		label: "Tags",
		value: "tags",
		type: "checkbox",
		defaultOpen: false,
		commandDisabled: false,
		options: [],
	},
];

export const companySheetFields: SheetField<Company, any>[] = [
	{
		id: "id",
		label: "Company ID",
		type: "readonly",
		skeletonClassName: "w-64",
	},
	{
		id: "createdAt",
		label: "Created",
		type: "timerange",
		skeletonClassName: "w-36",
	},
	{
		id: "name",
		label: "Company Name",
		type: "input",
		skeletonClassName: "w-48",
	},
	{
		id: "website",
		label: "Website",
		type: "input",
		skeletonClassName: "w-56",
	},
	{
		id: "industry",
		label: "Industry",
		type: "input",
		skeletonClassName: "w-32",
	},
	{
		id: "size",
		label: "Company Size",
		type: "input",
		skeletonClassName: "w-32",
	},
	{
		id: "phone",
		label: "Phone",
		type: "input",
		skeletonClassName: "w-32",
	},
	{
		id: "email",
		label: "Email",
		type: "input",
		skeletonClassName: "w-40",
	},
	{
		id: "contactCount",
		label: "Contact Count",
		type: "readonly",
		skeletonClassName: "w-16",
	},
];

export const propertyFilterFields: DataTableFilterField<Property>[] = [
	{
		label: "Created",
		value: "createdAt",
		type: "timerange",
		defaultOpen: true,
		commandDisabled: false,
	},
	{
		label: "Property Type",
		value: "propertyType",
		type: "checkbox",
		options: PROPERTY_TYPES.map((type) => ({
			label: type.label,
			value: type.value,
		})),
		commandDisabled: false,
	},
	{
		label: "Status",
		value: "status",
		type: "checkbox",
		options: PROPERTY_STATUSES.map((status) => ({
			label: status.label,
			value: status.value,
		})),
		commandDisabled: false,
	},
	{
		label: "Property Name",
		value: "name",
		type: "input",
		commandDisabled: false,
	},
	{
		label: ADDRESS_LABELS[ADDRESS_FIELDS.STREET],
		value: ADDRESS_FIELDS.STREET, // Changed from address.location.street to street
		type: "input",
		commandDisabled: false,
	},
	{
		label: ADDRESS_LABELS[ADDRESS_FIELDS.CITY],
		value: ADDRESS_FIELDS.CITY, // Changed from address.location.city to city
		type: "input",
		commandDisabled: false,
	},
	{
		label: ADDRESS_LABELS[ADDRESS_FIELDS.STATE],
		value: ADDRESS_FIELDS.STATE, // Changed from address.location.state to state
		type: "input",
		commandDisabled: false,
	},
	{
		label: ADDRESS_LABELS[ADDRESS_FIELDS.ZIP],
		value: ADDRESS_FIELDS.ZIP, // Changed from address.location.zip to zip
		type: "input",
		commandDisabled: false,
	},
	{
		label: "Price Range",
		value: "price",
		type: "slider",
		min: 0,
		max: 10000000,
		commandDisabled: false,
	},
	{
		label: "Bedrooms",
		value: "bedrooms",
		type: "slider",
		min: 0,
		max: 10,
		commandDisabled: false,
	},
	{
		label: "Bathrooms",
		value: "bathrooms",
		type: "slider",
		min: 0,
		max: 10,
		commandDisabled: false,
	},
	{
		label: "Square Footage",
		value: "squareFootage",
		type: "slider",
		min: 0,
		max: 50000,
		commandDisabled: false,
	},
	{
		label: "Year Built",
		value: "yearBuilt",
		type: "slider",
		min: 1800,
		max: new Date().getFullYear(),
		commandDisabled: false,
	},
	{
		label: "Lot Size",
		value: "lotSize",
		type: "slider",
		min: 0,
		max: 100,
		commandDisabled: false,
	},
	{
		label: "Units",
		value: "physicalDetails.units",
		type: "slider",
		min: 0,
		max: 200,
		commandDisabled: false,
	},
	{
		label: "Tags",
		value: "tags",
		type: "checkbox",
		defaultOpen: false,
		commandDisabled: false,
		options: [],
	},
];

export const propertySheetFields: SheetField<Property, any>[] = [
	{
		id: "id",
		label: "Property ID",
		type: "readonly",
		skeletonClassName: "w-64",
	},
	{
		id: "createdAt",
		label: "Created",
		type: "timerange",
		skeletonClassName: "w-36",
	},
	{
		id: "name",
		label: "Property Name",
		type: "input",
		skeletonClassName: "w-48",
	},
	{
		id: "address",
		label: "Address",
		type: "input",
		skeletonClassName: "w-56",
	},
	{
		id: "city",
		label: "City",
		type: "input",
		skeletonClassName: "w-32",
	},
	{
		id: "state",
		label: "State",
		type: "input",
		skeletonClassName: "w-24",
	},
	{
		id: "zipCode",
		label: "Zip Code",
		type: "input",
		skeletonClassName: "w-24",
	},
	{
		id: "propertyType",
		label: "Property Type",
		type: "checkbox",
		skeletonClassName: "w-32",
	},
	{
		id: "status",
		label: "Status",
		type: "checkbox",
		skeletonClassName: "w-24",
	},
	{
		id: "price",
		label: "Price",
		type: "input",
		skeletonClassName: "w-32",
	},
	{
		id: "bedrooms",
		label: "Bedrooms",
		type: "input",
		skeletonClassName: "w-24",
	},
	{
		id: "bathrooms",
		label: "Bathrooms",
		type: "input",
		skeletonClassName: "w-24",
	},
	{
		id: "squareFeet",
		label: "Square Feet",
		type: "input",
		skeletonClassName: "w-32",
	},
	{
		id: "summary",
		label: "Summary",
		type: "input",
		skeletonClassName: "w-full",
	},
	
];

export const LEVELS = ["success", "warning", "error"] as const;

// TODO: FIXME: improve this
export const _LEVELS = [...LEVELS, "info"] as const;

export function getLevelColor(
	value: (typeof _LEVELS)[number],
): Record<"text" | "bg" | "border", string> {
	switch (value) {
		case "success":
			return {
				text: "text-muted",
				bg: "bg-muted",
				border: "border-muted",
			};
		case "warning":
			return {
				text: "text-warning",
				bg: "bg-warning",
				border: "border-warning",
			};
		case "error":
			return {
				text: "text-error",
				bg: "bg-error",
				border: "border-error",
			};
		case "info":
		default:
			return {
				text: "text-info",
				bg: "bg-info",
				border: "border-info",
			};
	}
}

export function getLevelRowClassName(value: (typeof _LEVELS)[number]): string {
	switch (value) {
		case "success":
			return "";
		case "warning":
			return cn(
				"bg-warning/5 hover:bg-warning/10 data-[state=selected]:bg-warning/20 focus-visible:bg-warning/10",
				"dark:bg-warning/10 dark:hover:bg-warning/20 dark:data-[state=selected]:bg-warning/30 dark:focus-visible:bg-warning/20",
			);
		case "error":
			return cn(
				"bg-error/5 hover:bg-error/10 data-[state=selected]:bg-error/20 focus-visible:bg-error/10",
				"dark:bg-error/10 dark:hover:bg-error/20 dark:data-[state=selected]:bg-error/30 dark:focus-visible:bg-error/20",
			);
		case "info":
			return cn(
				"bg-info/5 hover:bg-info/10 data-[state=selected]:bg-info/20 focus-visible:bg-info/10",
				"dark:bg-info/10 dark:hover:bg-info/20 dark:data-[state=selected]:bg-info/30 dark:focus-visible:bg-info/20",
			);
		default:
			return "";
	}
}

export function getLevelLabel(value: (typeof _LEVELS)[number]): string {
	switch (value) {
		case "success":
			return "2xx";
		case "warning":
			return "4xx";
		case "error":
			return "5xx";
		default:
			return "Unknown";
	}
}

export function getStatusColor(
	value: number,
): Record<"text" | "bg" | "border", string> {
	if (value < 100 || value >= 600)
		return {
			text: "text-gray-500",
			bg: "bg-gray-100 dark:bg-gray-900/50",
			border: "border-gray-200 dark:border-gray-800",
		};
	switch (value.toString().charAt(0)) {
		case "1":
			return {
				text: "text-blue-500",
				bg: "bg-blue-100 dark:bg-blue-900/50",
				border: "border-blue-200 dark:border-blue-800",
			};
		case "2":
			return {
				text: "text-green-500",
				bg: "bg-green-100 dark:bg-green-900/50",
				border: "border-green-200 dark:border-green-800",
			};
		case "3":
			return {
				text: "text-yellow-500",
				bg: "bg-yellow-100 dark:bg-yellow-900/50",
				border: "border-yellow-200 dark:border-yellow-800",
			};
		case "4":
			return {
				text: "text-orange-500",
				bg: "bg-orange-100 dark:bg-orange-900/50",
				border: "border-orange-200 dark:border-orange-800",
			};
		case "5":
			return {
				text: "text-red-500",
				bg: "bg-red-100 dark:bg-red-900/50",
				border: "border-red-200 dark:border-red-800",
			};
		default:
			return {
				text: "text-gray-500",
				bg: "bg-gray-100 dark:bg-gray-900/50",
				border: "border-gray-200 dark:border-gray-800",
			};
	}
}
