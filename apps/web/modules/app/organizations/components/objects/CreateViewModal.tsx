"use client";

import {
	IconLayoutKanban,
	IconLock,
	IconMap,
	IconPlus,
	IconSearch,
	IconTable,
	IconWorld,
	IconX,
} from "@tabler/icons-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { getRandomColors } from "@ui/components/color-picker";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import {
	useCustomFieldDefinitions,
	useUpsertCustomFieldDefinition,
} from "../../../custom-field-definitions/lib/api";
import { PROPERTY_STATUSES } from "@app/organizations/components/objects/properties/schema";
import { ObjectType, ObjectViewType } from "@repo/database";
import { COMPANY_STAGE, COMPANY_STATUS, CONTACT_STAGE, CONTACT_STATUS, CUSTOM_OBJECT_STAGE, CUSTOM_OBJECT_STATUS, PROPERTY_STAGE, PROPERTY_STATUS } from "@app/shared/lib/constants";

interface CreateViewModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	objectType: ObjectType;
	organizationId: string;
	onViewCreated?: (viewId: string) => void;
	initialViewType?: ObjectViewType | null;
}

// Default column definitions for different object types
// const defaultColumnDefs = {
// 	contacts: [
// 		{ field: "firstName", headerName: "First Name", width: 150 },
// 		{ field: "lastName", headerName: "Last Name", width: 150 },
// 		{ field: "title", headerName: "Title", width: 150 },
// 		{ field: "email", headerName: "Email", width: 200 },
// 		{ field: "phone", headerName: "Phone", width: 150 },
// 		{ field: "company.name", headerName: "Company", width: 150 },
// 		{ field: "status", headerName: "Status", width: 120 },
// 		{ field: "createdAt", headerName: "Created", width: 150 },
// 	],
// 	companies: [
// 		{ field: "name", headerName: "Company Name", width: 200 },
// 		{ field: "industry", headerName: "Industry", width: 150 },
// 		{ field: "size", headerName: "Size", width: 120 },
// 		{ field: "website", headerName: "Website", width: 200 },
// 		{ field: "phone", headerName: "Phone", width: 150 },
// 		{ field: "email", headerName: "Email", width: 200 },
// 		{ field: "createdAt", headerName: "Created", width: 150 },
// 	],
// 	properties: [
// 		{ field: "name", headerName: "Property Name", width: 200 },
// 		{ field: "propertyType", headerName: "Type", width: 150 },
// 		{ field: "address", headerName: "Address", width: 200 },
// 		{ field: "status", headerName: "Status", width: 120 },
// 		{ field: "price", headerName: "Price", width: 150 },
// 		{ field: "bedrooms", headerName: "Bedrooms", width: 100 },
// 		{ field: "bathrooms", headerName: "Bathrooms", width: 100 },
// 		{ field: "squareFootage", headerName: "Sq Ft", width: 120 },
// 		{ field: "yearBuilt", headerName: "Year Built", width: 120 },
// 		{ field: "units", headerName: "Units", width: 100 },
// 	],
// 	custom_objects: [
// 		{ field: "title", headerName: "Title", width: 200 },
// 		{ field: "status", headerName: "Status", width: 120 },
// 		{ field: "description", headerName: "Description", width: 250 },
// 		{ field: "createdAt", headerName: "Created", width: 150 },
// 	],
// };

export function CreateViewModal({
	open,
	onOpenChange,
	objectType,
	organizationId,
	onViewCreated,
	initialViewType = null,
}: CreateViewModalProps) {
	const [selectedViewType, setSelectedViewType] =
		useState<ObjectViewType>("table");
	const [viewTitle, setViewTitle] = useState("");
	const [statusAttribute, setStatusAttribute] = useState("");
	const [isCreatingAttribute, setIsCreatingAttribute] = useState(false);
	const [newAttributeName, setNewAttributeName] = useState("");
	const [isPrivate, setIsPrivate] = useState(false);

	const queryClient = useQueryClient();

	// Fetch custom field definitions for this object type
	const { data: customFieldDefinitions = [] } = useCustomFieldDefinitions(
		objectType,
		organizationId,
		open, // Only fetch when modal is open
	);

	// Create custom field definition mutation
	const createCustomFieldMutation = useUpsertCustomFieldDefinition();

	const createViewMutation = useMutation({
		mutationFn: async (viewData: {
			name: string;
			objectType: string;
			organizationId: string;
			viewType: ObjectViewType;
			statusAttribute?: string;
			isPrivate: boolean;
		}) => {
			// Create the primary column based on object type
			const getPrimaryColumnDef = (objectType: string) => {
				const headerNames = {
					contact: "Contact",
					company: "Company Name", 
					property: "Property Name",
					custom_object: "Name"
				};
				
				return {
					field: "name",
					headerName: headerNames[objectType as keyof typeof headerNames] || "Name",
					width: 200,
					type: "text",
					visible: true
				};
			};

			// Create default mapConfig for map views
			const getDefaultMapConfig = (viewType: ObjectViewType) => {
				if (viewType === "map") {
					return {
						displayType: "table" as const,
						rowDensity: "normal" as const,
						showExportOptions: true,
						allowColumnReorder: true,
						showSearchBar: true,
					};
				}
				return undefined;
			};

			const response = await fetch("/api/object-views/views", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				credentials: "include",
				body: JSON.stringify({
					name: viewData.name,
					objectType: viewData.objectType,
					organizationId: viewData.organizationId,
					viewType: viewData.viewType,
					columnDefs: [getPrimaryColumnDef(viewData.objectType)], // Always include primary column
					statusAttribute: viewData.statusAttribute,
					mapConfig: getDefaultMapConfig(viewData.viewType), // Add default mapConfig for map views
					filters: [],
					filterCondition: "and",
					isDefault: false,
					isPublic: !viewData.isPrivate,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to create view");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("View created successfully");
			queryClient.invalidateQueries({
				queryKey: ["objectViews", organizationId, objectType],
			});
			onViewCreated?.(data.id);
			handleClose();
		},
		onError: (error) => {
			toast.error(error.message || "Failed to create view");
		},
	});

	const handleClose = () => {
		onOpenChange(false);
		// Reset form
		setSelectedViewType("table");
		setViewTitle("");
		setStatusAttribute("");
		setIsCreatingAttribute(false);
		setNewAttributeName("");
		setIsPrivate(false);
	};

	const handleCreateView = () => {
		createViewMutation.mutate({
			name: viewTitle,
			objectType,
			organizationId,
			viewType: selectedViewType,
			statusAttribute:
				selectedViewType === "kanban" ? statusAttribute : undefined,
			isPrivate,
		});
	};

	// Get select-type custom field definitions for the object type
	const selectFieldDefinitions = customFieldDefinitions.filter(
		(field) =>
			field.type === "select" &&
			field.options?.choices &&
			field.options.choices.length > 0,
	);

	const handleCreateAttribute = async () => {
		if (!newAttributeName.trim()) return;

		try {
			// Create default options for the new status field
			const defaultOptions = getDefaultOptionsForField(
				newAttributeName.trim(),
			);

			await createCustomFieldMutation.mutateAsync({
				name: newAttributeName
					.trim()
					.toLowerCase()
					.replace(/\s+/g, "_"),
				label: newAttributeName.trim(),
				type: "select",
				objectType,
				organizationId,
				options: {
					choices: defaultOptions,
				},
			});

			setStatusAttribute(
				newAttributeName.trim().toLowerCase().replace(/\s+/g, "_"),
			);
			setIsCreatingAttribute(false);
			setNewAttributeName("");
			toast.success(
				`"${newAttributeName.trim()}" field created successfully`,
			);
		} catch (error) {
			toast.error("Failed to create custom field");
			console.error(error);
		}
	};

	const getDefaultOptionsForField = (fieldName: string) => {
		const name = fieldName.toLowerCase();
		let options: Array<{ label: string; value: string }> = [];

		if (name.includes("status")) {
			switch (objectType) {
				case "contact":
					options = CONTACT_STATUS.map((status) => ({
						label: status.label,
						value: status.value,
					}));
					break;
				case "company":
					options = COMPANY_STATUS.map((status) => ({
						label: status.label,
						value: status.value,
					}));
					break;
				case "property":
					options = PROPERTY_STATUS.map((status) => ({
						label: status.label,
						value: status.value,
					}));
					break;
				default:
					options = CUSTOM_OBJECT_STATUS.map((status) => ({
						label: status.label,
						value: status.value,
					}));
			}
		} else if (name.includes("stage")) {
			switch (objectType) {
				case "contact":
					options = CONTACT_STAGE.map((stage) => ({
						label: stage.label,
						value: stage.value,
					}));
					break;
				case "company":
					options = COMPANY_STAGE.map((stage) => ({
						label: stage.label,
						value: stage.value,
					}));
					break;
				case "property":
					options = PROPERTY_STAGE.map((stage) => ({
						label: stage.label,
						value: stage.value,
					}));
					break;
				default:
					options = CUSTOM_OBJECT_STAGE.map((stage) => ({
						label: stage.label,
						value: stage.value,
					}));
			}
		} else {
			// Generic options for other field types
			options = [
				{ label: "Option 1", value: "option_1" },
				{ label: "Option 2", value: "option_2" },
				{ label: "Option 3", value: "option_3" },
			];
		}

		// Add random colors to each option
		const randomColors = getRandomColors(options.length);
		return options.map((option, index) => ({
			...option,
			color: randomColors[index],
		}));
	};

	// Set selectedViewType to initialViewType when modal opens
	useEffect(() => {
		if (open && initialViewType) {
			setSelectedViewType(initialViewType);
		} else if (!open) {
			setSelectedViewType("table");
		}
	}, [open, initialViewType]);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="!rounded-2xl border border-input bg-clip-padding p-2 pb-16 ring-4 ring-neutral-200/80 dark:bg-neutral-900 dark:ring-neutral-800 sm:max-w-3xl m-0">
				<div className="flex flex-col h-full">
					<DialogTitle className="sr-only">Create View</DialogTitle>
					<DialogDescription className="sr-only">
						Create a new view for your contacts
					</DialogDescription>
					<div className="flex items-center justify-between px-4 pt-2 pb-1">
						<div className="flex flex-row items-center gap-x-2">
							<span className="text-md font-light">
								Create View
							</span>
						</div>
					</div>
					<div className="flex flex-col px-2 pt-2 pb-4 mb-8">
						{/* View Type Selection */}
						<div className="space-y-3">
							<Label className="text-xs text-muted-foreground">
								View type
							</Label>
							<div
								className={`grid gap-4 ${objectType === "property" ? "grid-cols-3" : "grid-cols-2"}`}
							>
								{/* Table Option */}
								<div
									className={`cursor-pointer rounded-xl border p-4 transition-all ${
										selectedViewType === "table"
											? "border-blue-500 bg-blue-500/5"
											: "border-border hover:border-blue-500/50"
									}`}
									onClick={() => setSelectedViewType("table")}
								>
									<div className="flex items-center gap-3">
										<div className="rounded-lg bg-muted p-2 border border-border dark:border-zinc-700">
											<IconTable className="h-5 w-5" />
										</div>
										<div>
											<div className="font-medium">
												Table
											</div>
											<div className="text-sm text-muted-foreground">
												Organize your records on a table
											</div>
										</div>
									</div>
								</div>

								{/* Kanban Option */}
								<div
									className={`cursor-pointer rounded-xl border p-4 transition-all ${
										selectedViewType === "kanban"
											? "border-blue-500 bg-blue-500/5"
											: "border-border hover:border-blue-500/50"
									}`}
									onClick={() =>
										setSelectedViewType("kanban")
									}
								>
									<div className="flex items-center gap-3">
										<div className="rounded-lg bg-muted p-2 border border-border dark:border-zinc-700">
											<IconLayoutKanban className="h-5 w-5" />
										</div>
										<div>
											<div className="font-medium text-sm">
												Kanban
											</div>
											<div className="text-sm text-muted-foreground">
												Organize your records on a
												pipeline
											</div>
										</div>
									</div>
								</div>

								{/* Map Option - Only for properties */}
								{objectType === "property" && (
									<div
										className={`cursor-pointer rounded-xl border p-4 transition-all ${
											selectedViewType === "map"
												? "border-blue-500 bg-blue-500/5"
												: "border-border hover:border-blue-500/50"
										}`}
										onClick={() =>
											setSelectedViewType("map")
										}
									>
										<div className="flex items-center gap-3">
											<div className="rounded-lg bg-muted p-2 border border-border dark:border-zinc-700">
												<IconMap className="h-5 w-5" />
											</div>
											<div>
												<div className="font-medium text-sm">
													Map
												</div>
												<div className="text-sm text-muted-foreground">
													Visualize your properties on
													an interactive map
												</div>
											</div>
										</div>
									</div>
								)}
							</div>
						</div>

						{/* Title Input */}
						<div className="space-y-2 mt-4">
							<Label
								htmlFor="view-title"
								className="text-xs text-muted-foreground"
							>
								Title
							</Label>
							<Input
								id="view-title"
								placeholder="Enter a title for this view"
								value={viewTitle}
								onChange={(e) => setViewTitle(e.target.value)}
								className="w-full bg-background"
							/>
						</div>

						{/* Status Attribute (Kanban only) */}
						{selectedViewType === "kanban" && (
							<div className="space-y-3">
								<Label className="text-xs text-muted-foreground">
									Status attribute
								</Label>

								{!isCreatingAttribute ? (
									<Select
										value={statusAttribute}
										onValueChange={setStatusAttribute}
									>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Select a status attribute" />
										</SelectTrigger>
										<SelectContent>
											<div className="p-2">
												<Input placeholder="Search status attributes..." />
											</div>
											{selectFieldDefinitions.map(
												(field) => (
													<SelectItem
														key={field.id}
														value={field.name}
													>
														<div className="flex items-center gap-2">
															<span>
																{field.label}
															</span>
															<span className="text-xs text-muted-foreground">
																(
																{field.options
																	?.choices
																	?.length ||
																	0}{" "}
																options)
															</span>
														</div>
													</SelectItem>
												),
											)}
											<div className="border-t pt-2">
												<Button
													variant="ghost"
													className="w-full justify-start gap-2"
													onClick={() =>
														setIsCreatingAttribute(
															true,
														)
													}
												>
													<IconPlus className="h-4 w-4" />
													New Status Attribute
												</Button>
											</div>
										</SelectContent>
									</Select>
								) : (
									<div className="space-y-2">
										<div className="flex gap-2">
											<Input
												id="new-attribute"
												placeholder="Enter attribute name"
												value={newAttributeName}
												onChange={(e) =>
													setNewAttributeName(
														e.target.value,
													)
												}
												onKeyDown={(e) => {
													if (e.key === "Enter") {
														handleCreateAttribute();
													}
													if (e.key === "Escape") {
														setIsCreatingAttribute(
															false,
														);
														setNewAttributeName("");
													}
												}}
												autoFocus
												className="bg-background"
											/>
											<Button
												onClick={handleCreateAttribute}
												disabled={
													!newAttributeName.trim()
												}
											>
												Add
											</Button>
											<Button
												variant="ghost"
												size="icon"
												onClick={() => {
													setIsCreatingAttribute(
														false,
													);
													setNewAttributeName("");
												}}
											>
												<IconX className="h-4 w-4" />
											</Button>
										</div>
									</div>
								)}
							</div>
						)}

						{/* Privacy Setting */}
						<div className="space-y-3 mt-4">
							<Label className="text-xs text-muted-foreground">
								Privacy
							</Label>
							<div className="flex items-center justify-between p-3 rounded-xl border border-border">
								<div className="flex items-center gap-3">
									<div className="rounded-lg bg-muted p-2 border border-border dark:border-zinc-700">
										{isPrivate ? (
											<IconLock className="h-4 w-4" />
										) : (
											<IconWorld className="h-4 w-4" />
										)}
									</div>
									<div>
										<div className="font-medium text-sm">
											{isPrivate
												? "Private"
												: "Shared with workspace"}
										</div>
										<div className="text-xs text-muted-foreground">
											{isPrivate
												? "Only you can see this view"
												: "Anyone in the workspace can see this view"}
										</div>
									</div>
								</div>
								<Switch
									checked={isPrivate}
									onCheckedChange={setIsPrivate}
								/>
							</div>
						</div>
					</div>

					<div className="absolute inset-x-0 bottom-0 z-20 flex h-14 items-center justify-end gap-2 rounded-b-2xl border-t border-t-neutral-100 bg-neutral-50 px-4 dark:border-t-neutral-700 dark:bg-neutral-800">
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={() => onOpenChange(false)}
							disabled={createViewMutation.isPending}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							variant="primary"
							size="sm"
							disabled={createViewMutation.isPending}
							onClick={handleCreateView}
						>
							{createViewMutation.isPending ? (
								<div className="flex items-center gap-2">
									<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
									<span>Creating...</span>
								</div>
							) : (
								"Create View"
							)}
						</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
