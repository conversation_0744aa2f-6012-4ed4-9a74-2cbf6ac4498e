import { ObjectType } from "@repo/database";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import { CopyableValue } from "@shared/components/CopyableValue";
import { EmailCell } from "@shared/components/EmailCell";
import {
	IconAt,
	IconBrandFacebook,
	IconBrandInstagram,
	IconBrandLinkedin,
	IconBrandTwitter,
	IconBriefcase,
	IconBuilding,
	IconCake,
	IconCalendar,
	IconCalendarClock,
	IconChartColumn,
	IconCurrencyDollar,
	IconHash,
	IconHome,
	IconMapPin,
	IconNote,
	IconPhone,
	IconRuler,
	IconSignature,
	IconTag,
	IconUser,
	IconUsers,
	IconWorld,
} from "@tabler/icons-react";
import type { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@ui/components/badge";
import { Checkbox } from "@ui/components/checkbox";
import { format } from "date-fns";
import React from "react";

interface ViewColumnDef {
	field: string;
	headerName: string;
	width: number;
	type?: string;
}

const createCellRenderer = (field: string, objectType: ObjectType) => {
	return ({ row, getValue }: any) => {
		const value = getValue();

		switch (field) {
			case "id":
				return (
					<div className="w-[80px] font-mono text-xs">{value}</div>
				);

			case "name":
				// Don't render avatar here since TableRowComponent handles it for primary column
				return <span>{value}</span>;

			case "firstName":
			case "lastName":
			case "title":
			case "industry":
			case "size":
			case "propertyType":
				return <span>{value || ""}</span>;

			case "email":
				if (Array.isArray(row.original.email)) {
					const primaryEmail =
						row.original.email.find((e: any) => e.isPrimary) ||
						row.original.email[0];
					if (!primaryEmail) return "";
					
					// Get object name for subject line
					const objectName = row.original.name || 
						(row.original.firstName && row.original.lastName ? 
							`${row.original.firstName} ${row.original.lastName}` : 
							undefined);
					
					// Get all email addresses
					const allEmails = row.original.email.map((e: any) => e.address);
					
					return (
						<EmailCell
							email={primaryEmail.address}
							recipientName={objectName}
							subject={objectName ? `Regarding ${objectName}` : undefined}
							avatarUrl={row.original.image}
							type={objectType}
							allEmails={allEmails}
						/>
					);
				}
				if (value) {
					// Get object name for subject line
					const objectName = row.original.name || 
						(row.original.firstName && row.original.lastName ? 
							`${row.original.firstName} ${row.original.lastName}` : 
							undefined);
					
					return (
						<EmailCell 
							email={value} 
							recipientName={objectName}
							subject={objectName ? `Regarding ${objectName}` : undefined}
							avatarUrl={row.original.image}
							type={objectType}
						/>
					);
				}
				return "";

			case "phone":
				if (Array.isArray(row.original.phone)) {
					const primaryPhone =
						row.original.phone.find((p: any) => p.isPrimary) ||
						row.original.phone[0];
					if (!primaryPhone) return "";
					return (
						<CopyableValue
							value={primaryPhone.number}
							type="phone"
						/>
					);
				}
				if (value) {
					return <CopyableValue value={value} type="phone" />;
				}
				return "";

			case "website":
			case "linkedin":
			case "facebook":
			case "twitter":
			case "instagram":
			case "social.linkedin":
			case "social.facebook":
			case "social.twitter":
			case "social.instagram":
				if (!value) return "";
				return <CopyableValue value={value} type="url" />;

			case "company.name":
				return <span>{row.original.company?.name || ""}</span>;

			case "address.street":
				return <span>{row.original.address?.street || ""}</span>;

			case "address.city":
				return <span>{row.original.address?.city || ""}</span>;

			case "address.state":
				return <span>{row.original.address?.state || ""}</span>;

			case "address.zip":
				return <span>{row.original.address?.zip || ""}</span>;

			case "address.country":
				return <span>{row.original.address?.country || ""}</span>;

			case "address.location.street":
				return <span>{row.original.address?.street || ""}</span>;

			case "address.location.city":
				return <span>{row.original.address?.city || ""}</span>;

			case "address.location.state":
				return <span>{row.original.address?.state || ""}</span>;

			case "address.location.zip":
				return <span>{row.original.address?.zip || ""}</span>;

			case "address.location.country":
				return <span>{row.original.address?.country || ""}</span>;

			case "status":
			case "persona":
			case "stage": {
				if (!value) return "";

				const statusColors: Record<string, string> = {
					new: "bg-gray-100 text-gray-800",
					contacted: "bg-blue-100 text-blue-800",
					qualified: "bg-yellow-100 text-yellow-800",
					proposal: "bg-orange-100 text-orange-800",
					closed: "bg-green-100 text-green-800",
					cold: "bg-gray-100 text-gray-800",
					warm: "bg-yellow-100 text-yellow-800",
					hot: "bg-red-100 text-red-800",
					lead: "bg-blue-100 text-blue-800",
					prospect: "bg-purple-100 text-purple-800",
					customer: "bg-green-100 text-green-800",
				};

				const colorClass =
					statusColors[value.toLowerCase()] ||
					"bg-gray-100 text-gray-800";

				return (
					<Badge className={`capitalize ${colorClass}`}>
						{value}
					</Badge>
				);
			}

			case "contactCount":
				return (
					<div className="w-[80px] text-center">
						<Badge>{value || 0}</Badge>
					</div>
				);

			case "price":
				if (!value) return "";
				return (
					<span className="font-medium">
						${value.toLocaleString()}
					</span>
				);

			case "units":
				if (!value) return "";
				return (
					<span className="font-mono text-xs">
						{value.toLocaleString()}
					</span>
				);

			case "bedrooms":
			case "bathrooms":
			case "squareFootage":
			case "yearBuilt":
				if (!value) return "";
				return <span>{value.toLocaleString()}</span>;

			case "birthday":
				if (!value) return "";
				return <span>{format(new Date(value), "MMM d, yyyy")}</span>;

			case "createdAt":
			case "updatedAt":
				if (!value)
					return <span className="text-muted-foreground">—</span>;
				return <span>{format(new Date(value), "MMM d, yyyy")}</span>;

			case "summary":
				if (!value) return "";
				return (
					<span
						className="truncate max-w-[250px] block"
						title={value}
					>
						{value}
					</span>
				);

			case "spouseName":
			case "source":
				return (
					<span>
						{value || (
							<span className="text-muted-foreground">—</span>
						)}
					</span>
				);

			default:
				if (!value) return "";
				return <span>{String(value)}</span>;
		}
	};
};

const getHeaderIcon = (field: string) => {
	const iconMap: Record<string, any> = {
		id: IconHash,
		firstName: IconSignature,
		lastName: IconSignature,
		name: IconSignature,
		title: IconBriefcase,
		email: IconAt,
		phone: IconPhone,
		website: IconWorld,
		linkedin: IconBrandLinkedin,
		facebook: IconBrandFacebook,
		twitter: IconBrandTwitter,
		instagram: IconBrandInstagram,
		"social.linkedin": IconBrandLinkedin,
		"social.facebook": IconBrandFacebook,
		"social.twitter": IconBrandTwitter,
		"social.instagram": IconBrandInstagram,
		"company.name": IconBuilding,
		"address.street": IconMapPin,
		"address.city": IconMapPin,
		"address.state": IconMapPin,
		"address.zip": IconMapPin,
		"address.country": IconMapPin,
		"address.location.street": IconMapPin,
		"address.location.city": IconMapPin,
		"address.location.state": IconMapPin,
		"address.location.zip": IconMapPin,
		"address.location.country": IconMapPin,
		status: IconChartColumn,
		persona: IconUser,
		stage: IconChartColumn,
		industry: IconBriefcase,
		size: IconUsers,
		contactCount: IconUsers,
		price: IconCurrencyDollar,
		propertyType: IconHome,
		units: IconHome,
		bedrooms: IconHome,
		bathrooms: IconHome,
		squareFootage: IconRuler,
		yearBuilt: IconCalendar,
		birthday: IconCake,
		summary: IconNote,
		spouseName: IconUser,
		source: IconTag,
		createdAt: IconCalendarClock,
		updatedAt: IconCalendarClock,
	};

	return iconMap[field];
};

const createHeaderComponent = (headerName: string, field: string) => {
	const Icon = getHeaderIcon(field);

	return ({ column }: any) => (
		<div className="flex items-center gap-1 text-primary">
			{Icon && <Icon className="h-4 w-4" />}
			{headerName}
		</div>
	);
};

export function createDynamicColumns<TData = any>(
	columnDefs: ViewColumnDef[],
	objectType: ObjectType,
): ColumnDef<TData>[] {
	const selectColumn: ColumnDef<TData> = {
		id: "select",
		header: ({ table }: any) => (
			<Checkbox
				checked={
					table.getIsAllPageRowsSelected() ||
					(table.getIsSomePageRowsSelected() && "indeterminate")
				}
				onCheckedChange={(value) =>
					table.toggleAllPageRowsSelected(!!value)
				}
				aria-label="Select all"
			/>
		),
		cell: ({ row }: any) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label="Select row"
			/>
		),
		enableSorting: false,
		enableHiding: false,
		enableResizing: false,
		size: 40,
		minSize: 40,
		maxSize: 40,
	};

	const dynamicColumns = columnDefs.map((colDef) => {
		const { field, headerName, width } = colDef;

		const getColumnId = (fieldName: string) => {
			switch (fieldName) {
				case "company.name":
					return "company";
				case "social.linkedin":
					return "linkedin";
				case "social.facebook":
					return "facebook";
				case "social.twitter":
					return "twitter";
				case "social.instagram":
					return "instagram";
				// Map physicalDetails fields to top-level names for filter compatibility
				// Note: units mapping removed to avoid conflict with existing static column
				case "physicalDetails.bedrooms":
					return "bedrooms";
				case "physicalDetails.bathrooms":
					return "bathrooms";
				case "physicalDetails.squareFootage":
					return "squareFootage";
				case "physicalDetails.yearBuilt":
					return "yearBuilt";
				case "physicalDetails.lotSize":
					return "lotSize";
				// Map financials fields to top-level names for filter compatibility
				case "financials.price":
					return "price";
				default:
					return fieldName;
			}
		};

		const columnId = getColumnId(field);

		const socialFields = [
			"linkedin",
			"facebook",
			"twitter",
			"instagram",
			"social.linkedin",
			"social.facebook",
			"social.twitter",
			"social.instagram",
		];
		const isSocialField = socialFields.includes(field);

		const columnDef: ColumnDef<TData> = {
			id: columnId,
			accessorKey:
				field.includes(".") ||
				isSocialField ||
				field === "email" ||
				field === "phone" ||
				(field === "name" && objectType === "contact") // Add special case for contact name
					? undefined
					: field,
			accessorFn:
				field.includes(".") ||
				isSocialField ||
				field === "email" ||
				field === "phone" ||
				(field === "name" && objectType === "contact") // Add special case for contact name
					? (row: any) => {
							// Handle contact name computation
							if (field === "name" && objectType === "contact") {
								return `${row.firstName || ""} ${row.lastName || ""}`.trim() || "Unnamed Contact";
							}
							
							if (field === "email") {
								if (Array.isArray(row.email)) {
									const primaryEmail =
										row.email.find(
											(e: any) => e.isPrimary,
										) || row.email[0];
									return primaryEmail?.address || "";
								}
								return row.email || "";
							}
							if (field === "phone") {
								if (Array.isArray(row.phone)) {
									const primaryPhone =
										row.phone.find(
											(p: any) => p.isPrimary,
										) || row.phone[0];
									return primaryPhone?.number || "";
								}
								return row.phone || "";
							}
							if (field.startsWith("social.")) {
								const socialFieldName = field.split(".")[1];
								const value = row.social?.[socialFieldName];
								if (
									process.env.NODE_ENV === "development" &&
									field === "social.linkedin"
								) {
								}
								return value;
							}
							if (isSocialField && !field.includes(".")) {
								const value = row.social?.[field];
								return value;
							}
							const keys = field.split(".");
							let value = row;
							for (const key of keys) {
								value = value?.[key];
							}
							return value;
						}
					: undefined,
			header: createHeaderComponent(headerName, field),
			cell: createCellRenderer(field, objectType as ObjectType),
			size: width,
			minSize: Math.max(80, Math.floor(width * 0.6)),
			maxSize: Math.max(300, Math.floor(width * 1.5)),
			enableSorting:
				!field.includes(".") && field !== "id" && !isSocialField,
			enableHiding: field !== "id",
			filterFn: (row, id, value) => {
				const cellValue = row.getValue(id);
				
				// Special handling for tags array
				if (field === "tags") {
					if (!cellValue || !Array.isArray(cellValue) || !Array.isArray(value)) return false;
					// cellValue is an array of tag objects, value is an array of tag names to filter by
					const tagNames = cellValue.map((tag: any) => tag.name?.toLowerCase());
					const filterNames = value.map((tag: string) => tag.toLowerCase());
					// Check if any of the selected tag names exist in the record's tag names
					return filterNames.some((filterName: string) => tagNames.includes(filterName));
				}

				// Default handling for other fields
				if (!cellValue || !value) return false;
				return String(cellValue)
					.toLowerCase()
					.includes(String(value).toLowerCase());
			},
		};

		if (field === "id") {
			columnDef.enableSorting = false;
			columnDef.enableHiding = false;
		}

		if (field === "email" || field === "phone") {
			columnDef.filterFn = (row, id, value) => {
				const cellValue = row.getValue(id);
				if (!cellValue || !value) return false;
				return String(cellValue)
					.toLowerCase()
					.includes(String(value).toLowerCase());
			};
		}

		if (
			[
				"status",
				"persona",
				"stage",
				"industry",
				"size",
				"propertyType",
				"tags",
			].includes(field)
		) {
			columnDef.filterFn = (row, id, value) => {
				const cellValue = row.getValue(id);
				
				// Special handling for tags array
				if (field === "tags") {
					if (!cellValue || !Array.isArray(cellValue) || !Array.isArray(value)) return false;
					// cellValue is an array of tag objects, value is an array of tag names to filter by
					const tagNames = cellValue.map((tag: any) => tag.name?.toLowerCase());
					const filterNames = value.map((tag: string) => tag.toLowerCase());
					// Check if any of the selected tag names exist in the record's tag names
					return filterNames.some((filterName: string) => tagNames.includes(filterName));
				}

				// Default handling for other fields
				if (!cellValue || !value) return false;
				return String(cellValue)
					.toLowerCase()
					.includes(String(value).toLowerCase());
			};
		}

		return columnDef;
	});

	return [selectColumn, ...dynamicColumns];
}
