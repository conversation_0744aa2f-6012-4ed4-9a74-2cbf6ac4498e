"use client";

import {
	Icon<PERSON>rrowDown,
	IconArrowLeft,
	IconArrowRight,
	IconArrowUp,
	IconEyeOff,
	IconPencilOff,
} from "@tabler/icons-react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	TableHead,
} from "@ui/components/table";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { flexRender } from "@tanstack/react-table";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import * as React from "react";

interface HeaderCellProps {
	header: any;
	onMoveColumn?: (id: string, direction: "left" | "right") => void;
	primaryColumn?: string;
	readonlyColumns?: string[];
	dragOverId?: string | null;
	onHide?: (e: React.MouseEvent, columnId: string) => void;
}

export function HeaderCell({
	header,
	onMoveColumn,
	primaryColumn,
	readonlyColumns = [],
	dragOverId,
	onHide,
}: HeaderCellProps) {
	if (header.isPlaceholder) return null;

	const canSort = header.column.getCanSort();
	const canHide = header.column.getCanHide();
	const sorted = header.column.getIsSorted();
	const width = header.column.getSize();
	const isPinned = header.column.getIsPinned();
	const isReadonly = readonlyColumns.includes(header.column.id);

	const columnAccessorKey = (header.column.columnDef as any)?.accessorKey;

	// Check if this is the primary column (could match by id or accessorKey)
	const isPrimaryColumn =
		header.column.id === primaryColumn ||
		columnAccessorKey === primaryColumn ||
		header.column.columnDef.id === primaryColumn;

	// Calculate left position for pinned columns
	const getLeftPosition = () => {
		if (header.column.id === "select") return "left-0";
		if (isPrimaryColumn) return "";
		return "";
	};

	// Get the style with proper left positioning
	const getColumnStyle = () => {
		// Always force select column to 40px regardless of what getSize() returns
		const actualWidth = header.column.id === "select" ? 40 : width;

		const baseStyle = {
			width: actualWidth,
			minWidth: actualWidth,
			maxWidth: actualWidth,
			// For select column, add extra CSS properties to force the width
			...(header.column.id === "select" && {
				flex: "0 0 40px" as any,
				boxSizing: "border-box" as any,
				overflow: "hidden" as any,
				textOverflow: "clip" as any,
			}),
		};

		if (isPrimaryColumn) {
			return {
				...baseStyle,
				left: "40px",
			};
		}

		return baseStyle;
	};

	// Handle pinned columns (select and primary) - not draggable
	if (header.column.id === "select" || isPrimaryColumn) {
		return (
			<TableHead
				key={header.id}
				colSpan={header.colSpan}
				style={getColumnStyle()}
				className={cn(
					"h-10 text-left align-middle font-medium text-muted-foreground select-none",
					"relative border-b border-t border-border group",
					header.column.id === "select"
						? "select-column-header !w-10 !min-w-10 !max-w-10 !px-0 !p-0"
						: "px-2",
					isPinned && [
						"sticky z-50 bg-sidebar",
						isPrimaryColumn &&
							"after:absolute after:right-0 after:top-0 after:h-full after:w-px after:bg-border shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]",
						getLeftPosition(),
					],
				)}
			>
				{header.column.id === "select" ? (
					<div className="flex items-center justify-center w-full h-full">
						{flexRender(
							header.column.columnDef.header,
							header.getContext(),
						)}
					</div>
				) : (
					<div className="flex items-center justify-between w-full gap-2 min-w-0 px-2">
						<div className="truncate flex-1 min-w-0">
							{flexRender(
								header.column.columnDef.header,
								header.getContext(),
							)}
						</div>
						<div className="flex items-center gap-1 flex-shrink-0">
							{isReadonly && (
								<IconPencilOff className="size-3.5 text-muted-foreground" />
							)}
							{sorted && (
								<span>
									{sorted === "desc" ? (
										<IconArrowDown className="size-3.5" />
									) : (
										<IconArrowUp className="size-3.5" />
									)}
								</span>
							)}
						</div>
					</div>
				)}
				{header.column.getCanResize() &&
					header.column.id !== "select" && (
						<div
							onDoubleClick={(e) => {
								e.preventDefault();
								e.stopPropagation();
								header.column.resetSize();
							}}
							onMouseDown={(e) => {
								e.preventDefault();
								e.stopPropagation();
								header.getResizeHandler()(e);
							}}
							onTouchStart={(e) => {
								e.preventDefault();
								e.stopPropagation();
								header.getResizeHandler()(e);
							}}
							className={cn(
								"absolute right-0 top-0 h-full w-0.5 cursor-col-resize select-none touch-none z-20",
								"bg-transparent hover:bg-blue-500 active:bg-blue-500",
								header.column.getIsResizing() &&
									"bg-blue-500 opacity-100",
							)}
						/>
					)}
			</TableHead>
		);
	}

	// Draggable header cell for non-pinned columns
	return (
		<DraggableHeaderCell
			header={header}
			onMoveColumn={onMoveColumn}
			primaryColumn={primaryColumn}
			readonlyColumns={readonlyColumns}
			dragOverId={dragOverId}
			onHide={onHide}
		/>
	);
}

function DraggableHeaderCell({
	header,
	onMoveColumn,
	primaryColumn,
	readonlyColumns = [],
	dragOverId,
	onHide,
}: HeaderCellProps) {
	const canSort = header.column.getCanSort();
	const canHide = header.column.getCanHide();
	const sorted = header.column.getIsSorted();
	const width = header.column.getSize();
	const isPinned = header.column.getIsPinned();
	const isReadonly = readonlyColumns.includes(header.column.id);

	const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);
	const dropdownTimerRef = React.useRef<NodeJS.Timeout | null>(null);

	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({
		id: header.column.id,
	});

	const style = {
		transform: CSS.Translate.toString(transform),
		transition,
		opacity: isDragging ? 0.5 : 1,
		position: "relative" as const,
		zIndex: isDragging ? 1 : 0,
	};

	const getColumnStyle = () => {
		const actualWidth = width;

		const baseStyle = {
			width: actualWidth,
			minWidth: actualWidth,
			maxWidth: actualWidth,
		};

		return baseStyle;
	};

	// Handle manual dropdown control to work with drag sensors
	const handleManualClick = React.useCallback(
		(e: React.MouseEvent) => {
			e.stopPropagation();

			if (dropdownTimerRef.current) {
				clearTimeout(dropdownTimerRef.current);
				dropdownTimerRef.current = null;
			}

			if (isDragging) {
				return;
			}

			dropdownTimerRef.current = setTimeout(() => {
				if (!isDragging) {
					setIsDropdownOpen(true);
				}
				dropdownTimerRef.current = null;
			}, 150);
		},
		[header.column.id, isDragging],
	);

	// Close dropdown when drag starts
	React.useEffect(() => {
		if (isDragging) {
			setIsDropdownOpen(false);
			if (dropdownTimerRef.current) {
				clearTimeout(dropdownTimerRef.current);
				dropdownTimerRef.current = null;
			}
		}
	}, [isDragging, header.column.id]);

	// Cleanup timer on unmount
	React.useEffect(() => {
		return () => {
			if (dropdownTimerRef.current) {
				clearTimeout(dropdownTimerRef.current);
			}
		};
	}, []);

	return (
		<TableHead
			ref={setNodeRef}
			key={header.id}
			colSpan={header.colSpan}
			style={{ ...getColumnStyle(), ...style }}
			className={cn(
				"h-10 text-left align-middle font-medium text-muted-foreground select-none",
				"relative border-b border-t border-border group",
				"px-2",
				isDragging &&
					"opacity-50 bg-blue-500/50 border-blue-500 border-l",
				isPinned && [
					"sticky z-50 bg-sidebar",
					"after:absolute after:right-0 after:top-0 after:h-full after:w-px after:bg-border shadow-[2px_0_8px_-2px_rgba(0,0,0,0.1)]",
				],
			)}
			{...attributes}
			{...listeners}
		>
			<DropdownMenu
				open={isDropdownOpen}
				onOpenChange={setIsDropdownOpen}
			>
				<DropdownMenuTrigger asChild>
					<div
						role="button"
						tabIndex={0}
						className={cn(
							"flex items-center justify-between w-full gap-2 pr-2 h-full min-w-0 cursor-pointer",
						)}
						style={{
							paddingRight: header.column.getCanResize()
								? "12px"
								: "8px",
						}}
						onClick={handleManualClick}
						onDoubleClick={(e) => {
							e.stopPropagation();
							if (dropdownTimerRef.current) {
								clearTimeout(dropdownTimerRef.current);
								dropdownTimerRef.current = null;
							}
							setIsDropdownOpen(true);
						}}
						onKeyDown={(e) => {
							if (e.key === "Enter" || e.key === " ") {
								e.preventDefault();
								setIsDropdownOpen(true);
							}
						}}
					>
						<div className="truncate flex-1 min-w-0">
							{flexRender(
								header.column.columnDef.header,
								header.getContext(),
							)}
						</div>
						<div className="flex items-center gap-1 flex-shrink-0">
							{isReadonly && (
								<Tooltip>
									<TooltipTrigger>
										<IconPencilOff className="size-3.5 text-muted-foreground" />
									</TooltipTrigger>
									<TooltipContent side="right">
										This column is read-only
									</TooltipContent>
								</Tooltip>
							)}
							{sorted && (
								<span>
									{sorted === "desc" ? (
										<IconArrowDown className="size-3.5" />
									) : (
										<IconArrowUp className="size-3.5" />
									)}
								</span>
							)}
						</div>
					</div>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					align="start"
					style={{ width: Math.max(width, 220) }}
					className="min-w-[220px]"
				>
					{canSort && (
						<>
							<DropdownMenuItem
								onClick={() =>
									header.column.toggleSorting(false)
								}
								icon2={<IconArrowUp className="size-3.5" />}
								className="w-full"
							>
								Sort ascending
							</DropdownMenuItem>
							<DropdownMenuItem
								onClick={() =>
									header.column.toggleSorting(true)
								}
								icon2={<IconArrowDown className="size-3.5" />}
								className="w-full"
							>
								Sort descending
							</DropdownMenuItem>
						</>
					)}
					{(canSort || canHide) && <DropdownMenuSeparator />}
					{onMoveColumn && (
						<>
							<DropdownMenuItem
								onClick={() =>
									onMoveColumn(header.column.id, "left")
								}
								icon2={<IconArrowLeft className="size-4" />}
								className="w-full"
							>
								Move left
							</DropdownMenuItem>
							<DropdownMenuItem
								onClick={() =>
									onMoveColumn(header.column.id, "right")
								}
								icon2={<IconArrowRight className="size-4" />}
								className="w-full"
							>
								Move right
							</DropdownMenuItem>
						</>
					)}
					<DropdownMenuItem
						onClick={() => {
							if (onHide) {
								onHide(
									{} as React.MouseEvent,
									header.column.id,
								);
							}
						}}
						icon2={<IconEyeOff className="size-4" />}
						className="w-full"
					>
						Hide column
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
			{header.column.getCanResize() && header.column.id !== "select" && (
				<div
					onDoubleClick={(e) => {
						e.preventDefault();
						e.stopPropagation();
						header.column.resetSize();
					}}
					onMouseDown={(e) => {
						e.preventDefault();
						e.stopPropagation();
						header.getResizeHandler()(e);
					}}
					onTouchStart={(e) => {
						e.preventDefault();
						e.stopPropagation();
						header.getResizeHandler()(e);
					}}
					className={cn(
						"absolute right-0 top-0 h-full w-0.5 cursor-col-resize select-none touch-none z-30",
						"bg-transparent hover:bg-blue-500 active:bg-blue-500",
						header.column.getIsResizing() &&
							"bg-blue-500 opacity-100",
					)}
				/>
			)}
		</TableHead>
	);
} 