"use client";

import PropertySidebar from "@app/organizations/components/PropertySidebar";
import { useCallback, useEffect, useRef, useState } from "react";
import { ObjectsHeader } from "../../ObjectsHeader";
import { propertiesConfig } from "../../properties/config";

import "mapbox-gl/dist/mapbox-gl.css";
import "@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css";
import mapboxgl from "mapbox-gl";

import { MapboxOverlay } from "@deck.gl/mapbox";
import { ScatterplotLayer, ArcLayer } from "@deck.gl/layers";
import { H3HexagonLayer } from "@deck.gl/geo-layers";

import { MapControls } from "./components/MapControls";
import {
	useAutocomplete,
	useMapbox,
	useMapControls,
	useMapMarkers,
	usePropertyBoundaries,
	usePropertyData,
	usePropertySearch,
	useViewportPropertyData,
	useOptimizedMapMarkers,
	useMapPerformanceMonitor,
	useOptimizationRecommendations,
} from "./hooks";
import type {
	AutocompleteResult,
	MapViewProps,
	PropertiesMapProps,
} from "./types";
import { PropertyTable } from "@app/organizations/components/objects/views/map/components/PropertyTable";
import { PropertyGrid } from "@app/organizations/components/objects/views/map/components/PropertyGrid";
import { cn } from "@ui/lib";
import { DataTableFilterCommand } from "@app/organizations/components/objects/shared/data-table/data-table-filter-command";
import { DataTableProvider } from "@app/organizations/components/objects/shared/data-table/data-table-provider";
import { createColumnHelper, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { propertySearchParams } from "../../properties/search-params";
import { propertyFilterFields } from "../../../../lib/constants";
import type { Property as PropertyType } from "../../properties/schema";
import type { DataTableFilterField } from "@app/organizations/components/objects/shared/data-table/types";

// 3D Buildings layer configuration for Mapbox
const buildings3DLayer: any = {
	id: '3d-buildings',
	source: 'composite',
	'source-layer': 'building',
	filter: ['==', 'extrude', 'true'],
	type: 'fill-extrusion',
	minzoom: 14,
	paint: {
		'fill-extrusion-color': '#ccc',
		'fill-extrusion-height': ['get', 'height'],
	}
};

// Helper function to find the first label layer ID
const getFirstLabelLayerId = (style: any) => {
	const layers = style?.layers;
	if (!layers) return undefined;
	
	// Find the index of the first symbol (i.e. label) layer in the map style
	for (let i = 0; i < layers.length; i++) {
		if (layers[i].type === 'symbol') {
			return layers[i].id;
		}
	}
	return undefined;
};

// Custom hook for deck.gl integration optimized for 1M+ records
const useDeckGL = (map: any, properties: any[], clusters: any[] = [], selectedProperty: any, show3D: boolean = false, zoom: number = 10, performanceConfig: any) => {
	const overlayRef = useRef<MapboxOverlay | null>(null);

	useEffect(() => {
		if (!map || (!properties && !clusters)) return;

		// Simplified rendering logic - focus on getting basic functionality working
		const totalDataPoints = properties.length + clusters.length;
		const useSimplifiedRendering = zoom < 10 || totalDataPoints > 5000;
		const shouldShowIndividualProperties = zoom > 10;

		// Process individual properties with performance optimizations
		const propertyData = shouldShowIndividualProperties ? properties.filter(p => {
			const location = p.location || p.coordinates;
			const coords = location?.coordinates || location?.location?.coordinates;
			return coords && Array.isArray(coords) && coords.length === 2;
		}).map(property => {
			const location = property.location || property.coordinates;
			const coords = location?.coordinates || location?.location?.coordinates;
			return {
				position: [coords[0], coords[1]],
				property: property,
				selected: selectedProperty?.id === property.id,
				type: property.propertyType || 'other',
				value: property.financials?.currentPrice || property.price || 0,
				isCluster: false,
			};
		}) : [];

		// Process clusters with enhanced visualization
		const clusterData = clusters.map(cluster => ({
			position: cluster.position,
			cluster: cluster,
			selected: false,
			type: 'cluster',
			value: cluster.averagePrice || 0,
			isCluster: true,
			count: cluster.count,
			// Add density-based sizing
			density: Math.min(cluster.count / 100, 5), // Normalize density for sizing
		}));

		// Combine data efficiently - prioritize clusters at low zoom, ensure we always have some data
		let allData = [];
		if (zoom <= 10 && clusterData.length > 0) {
			// Low zoom: show only clusters
			allData = clusterData;
		} else if (propertyData.length > 0) {
			// High zoom: show properties and any clusters
			allData = [...propertyData, ...clusterData];
		} else if (clusterData.length > 0) {
			// Fallback: show clusters if no individual properties
			allData = clusterData;
		} else {
			// Last resort: show some basic data if we have any properties
			allData = properties.slice(0, 100).filter(p => {
				const location = p.location || p.coordinates;
				const coords = location?.coordinates || location?.location?.coordinates;
				return coords && Array.isArray(coords) && coords.length === 2;
			}).map(property => {
				const location = property.location || property.coordinates;
				const coords = location?.coordinates || location?.location?.coordinates;
				return {
					position: [coords[0], coords[1]],
					property: property,
					selected: selectedProperty?.id === property.id,
					type: property.propertyType || 'other',
					value: 0,
					isCluster: false,
				};
			});
		}

		const propertyLayer = new ScatterplotLayer({
			id: 'property-cluster-scatter',
			data: allData,
			pickable: true,
			// Optimize opacity and rendering for large datasets
			opacity: useSimplifiedRendering ? 0.7 : (show3D ? 0.9 : 0.8),
			stroked: !useSimplifiedRendering, // Remove strokes for performance on large datasets
			filled: true,
			extruded: show3D && !useSimplifiedRendering, // Disable 3D for simplified rendering
			// Simplified sizing for performance
			radiusScale: useSimplifiedRendering ? 1 : (show3D ? 12 : 6),
			radiusMinPixels: useSimplifiedRendering ? performanceConfig.dotSizeMin : (show3D ? 15 : 8),
			radiusMaxPixels: useSimplifiedRendering ? performanceConfig.dotSizeMax : (show3D ? 80 : 40),
			lineWidthMinPixels: useSimplifiedRendering ? 0 : (show3D ? 3 : 2),
			elevationScale: (show3D && !useSimplifiedRendering) ? 3 : 0,
			getPosition: (d: any) => d.position,
			getRadius: (d: any) => {
				if (useSimplifiedRendering) {
					// Use fixed small sizes for performance
					return d.isCluster ? 
						Math.min(performanceConfig.dotSizeMin + Math.log10(d.count), performanceConfig.dotSizeMax) :
						d.selected ? performanceConfig.dotSizeMax : performanceConfig.dotSizeMin;
				}
				
				if (d.isCluster) {
					// Dynamic cluster sizing based on count and density
					const baseRadius = show3D ? 30 : 20;
					const sizeMultiplier = Math.log(d.count) * 3 + (d.density || 1);
					return Math.min(baseRadius + sizeMultiplier, show3D ? 80 : 40);
				}
				const baseRadius = show3D ? 20 : 10;
				return d.selected ? baseRadius * 1.5 : baseRadius;
			},
			getElevation: (d: any) => {
				if (!show3D || useSimplifiedRendering) return 0;
				if (d.isCluster) {
					// Clusters get elevation based on density
					return 200 + Math.log(d.count) * 50 + (d.density * 20);
				}
				// Individual properties
				const baseElevation = d.selected ? 300 : 150;
				const typeMultiplier = d.type === 'commercial' ? 1.8 : 
								   d.type === 'industrial' ? 1.4 : 
								   d.type === 'residential' ? 1.0 : 0.8;
				return baseElevation * typeMultiplier;
			},
			getFillColor: (d: any) => {
				if (d.selected) return [255, 0, 128, 255]; // Bright pink for selected
				
				if (d.isCluster) {
					// Enhanced cluster visualization with density-based intensity
					const alpha = useSimplifiedRendering ? 180 : (show3D ? 220 : 200);
					const densityAlpha = Math.min(alpha + (d.density * 10), 255);
					return [112, 72, 232, densityAlpha]; // Purple with density-based alpha
				}
				
				// Simplified colors for performance
				const alpha = useSimplifiedRendering ? 160 : (show3D ? 200 : 180);
				switch (d.type) {
					case 'residential': return [0, 128, 255, alpha]; // Blue
					case 'commercial': return [255, 165, 0, alpha]; // Orange
					case 'industrial': return [128, 128, 128, alpha]; // Gray
					case 'land': return [34, 139, 34, alpha]; // Green
					default: return [148, 0, 211, alpha]; // Purple for other/unknown
				}
			},
			getLineColor: (d: any) => {
				if (useSimplifiedRendering) return [0, 0, 0, 0]; // No strokes for performance
				if (d.isCluster) return [255, 255, 255, 255]; // White border for clusters
				return d.selected ? [255, 255, 255, 255] : [255, 255, 255, 150];
			},
			wireframe: false,
			onClick: (info: any) => {
				if (info.object) {
					if (info.object.isCluster && info.object.cluster) {
						// Handle cluster click - zoom to cluster bounds
						const cluster = info.object.cluster;
						if (cluster.bounds && map) {
							const bounds = new mapboxgl.LngLatBounds(
								[cluster.bounds.west, cluster.bounds.south],
								[cluster.bounds.east, cluster.bounds.north]
							);
							map.fitBounds(bounds, {
								padding: 50,
								maxZoom: 16,
							});
						}
					} else if (info.object.property) {
						// Handle individual property click
						const property = info.object.property;
						if (typeof window !== 'undefined' && (window as any).__deckgl_property_select) {
							(window as any).__deckgl_property_select(property);
						}
					}
				}
			}
		});

		// Optimize connections layer - only show for detailed view and limit connections
		const connectionsLayer = (selectedProperty && !useSimplifiedRendering && zoom > 14) ? new ArcLayer({
			id: 'property-connections',
			data: propertyData.filter(p => 
				p.property.id !== selectedProperty.id && 
				getDistance(p.position, (() => {
					const location = selectedProperty.location || selectedProperty.coordinates;
					const coords = location?.coordinates || location?.location?.coordinates;
					return coords && Array.isArray(coords) ? [coords[0], coords[1]] : [0, 0];
				})()) < 0.005 // Reduced radius for performance
			).slice(0, 3), // Limit to 3 connections for performance
			pickable: false,
			getSourcePosition: () => {
				const location = selectedProperty.location || selectedProperty.coordinates;
				const coords = location?.coordinates || location?.location?.coordinates;
				return coords && Array.isArray(coords) ? [coords[0], coords[1]] : [0, 0];
			},
			getTargetPosition: (d: any) => d.position,
			getSourceColor: show3D ? [255, 0, 128, 180] : [255, 0, 128, 120],
			getTargetColor: show3D ? [0, 200, 255, 180] : [0, 128, 255, 120],
			getWidth: show3D ? 3 : 1.5,
			getHeight: show3D ? 0.3 : 0.2,
			opacity: 0.6,
			// Performance optimization
			updateTriggers: {
				getSourcePosition: [selectedProperty.id],
			}
		}) : null;

		// Remove heatmap layer for performance with large datasets
		// Focus on simple, efficient rendering

		const layers = [
			propertyLayer,
			connectionsLayer
		].filter(Boolean);

		// Create or update overlay
		if (!overlayRef.current) {
			overlayRef.current = new MapboxOverlay({
				interleaved: true,
				layers: layers
			});
			map.addControl(overlayRef.current);
		} else {
			overlayRef.current.setProps({ layers });
		}

		return () => {
			if (overlayRef.current && map) {
				try {
					map.removeControl(overlayRef.current);
					overlayRef.current = null;
				} catch (e) {
					console.warn('Error removing deck.gl overlay:', e);
				}
			}
		};
	}, [map, properties, clusters, selectedProperty, show3D, zoom]);

	return overlayRef.current;
};

// Helper function to calculate distance between two coordinates
const getDistance = (coord1: number[], coord2: number[]) => {
	const [lng1, lat1] = coord1;
	const [lng2, lat2] = coord2;
	return Math.sqrt(Math.pow(lng2 - lng1, 2) + Math.pow(lat2 - lat1, 2));
};

type SelectedPropertyType = {
	organizationId: string;
	id: string;
	name: string;
	recordType: "property";
	isDeleted: boolean;
	createdBy: string;
	updatedBy: string;
	status: string;
	unitMixes: any[];
	saleHistory: any[];
	mlsHistory: any[];
	mortgages: any[];
	propertyInfo: any;
	propertyType: string;
	location?: {
		type: "Point";
		coordinates: number[];
	};
	createdAt: string | Date;
	updatedAt: string | Date;
	creator?: { [key: string]: any } | undefined;
};

function PropertiesMap({
	organizationId,
	renderHeader,
	view,
	onViewRenamed,
}: PropertiesMapProps) {
	// 🚀 OPTIMIZED FOR 1M+ RECORDS - Performance-First Architecture
	//
	// This map implementation is designed to efficiently handle massive datasets (1M+ properties)
	// using a multi-layered optimization strategy:
	//
	// 📍 **Data Loading Strategy:**
	// • Viewport-based loading: Only loads data within visible area + buffer
	// • Server-side clustering: Aggregates nearby properties at low zoom levels
	// • Progressive disclosure: Shows different detail levels based on zoom
	// • Debounced API calls: Prevents excessive requests during navigation
	// • Memory limits: Caps at 50k properties in memory for stable performance
	//
	// 🎨 **Rendering Optimizations:**
	// • WebGL-only rendering: Uses deck.gl for GPU acceleration (no legacy markers)
	// • Simplified dots: Small, efficient circles instead of complex markers
	// • Conditional features: 3D effects and strokes only when beneficial
	// • Layer prioritization: Clusters at low zoom, individuals at high zoom
	// • No heatmaps: Removed performance-intensive overlays
	//
	// 🔧 **Smart Performance Modes:**
	// • Simplified Mode (zoom < 10): Minimal rendering, clustering prioritized
	// • Detailed Mode (zoom > 10): Enhanced visuals with full features
	// • Memory monitoring: Real-time performance stats and recommendations
	// • Adaptive sizing: Dynamic dot sizes based on density and zoom
	//
	// 🎯 **User Experience:**
	// • Instant responsiveness at any zoom level
	// • Smooth clustering transitions
	// • Clear performance feedback
	// • Graceful degradation under load
	const mapContainer = useRef<HTMLDivElement>(null);
	const [selectedProperty, setSelectedProperty] = useState<PropertyType | null>(null);
	const [firstLabelLayerId, setFirstLabelLayerId] = useState<string | undefined>();
	const { map, draw, mapLoaded } = useMapbox(mapContainer);
	const { controlsState, updateControls } = useMapControls();
	// Optimized performance configuration for handling 64k+ properties with efficient clustering
	const performanceConfig = {
		useOptimizedData: true, // ENABLED: Use viewport-based data loading for 64k+ properties
		maxPropertiesInMemory: 50000, // Increased: Allow up to 50k properties in memory
		enableClustering: true, // Essential for 64k+ properties
		clusterThreshold: 14, // Increased: Cluster up to zoom level 14 for better performance
		maxClusterZoom: 16, // Increased: Allow clustering at higher zoom levels
		clusterRadius: 80, // Increased cluster radius for better grouping
		cacheTTL: 600000, // Increased: Cache for 10 minutes for large datasets
		debounceMs: 200, // Reduced: Faster response while still preventing excessive calls
		viewportBuffer: 0.2, // Increased: 20% buffer for smoother navigation
		simplificationZoom: 12, // Increased: Use simplified rendering below zoom 12
		dotSizeMin: 6, // Larger minimum dots for better visibility at scale
		dotSizeMax: 20, // Larger maximum dots for clusters
		// New clustering configuration for 64k+ properties
		clusterMaxZoom: 16, // Maximum zoom to cluster at
		clusterMinPoints: 2, // Minimum points to form a cluster
		// Performance thresholds for different zoom levels
		zoomThresholds: {
			lowZoom: 10, // Below this: only clusters, no individual properties
			mediumZoom: 14, // 10-14: clusters + some individual properties
			highZoom: 16, // Above 14: individual properties + small clusters
		},
		// Data loading limits by zoom level for 64k+ datasets
		loadingLimits: {
			lowZoom: 64000, // Load all properties for clustering at low zoom
			mediumZoom: 20000, // Load 20k properties at medium zoom
			highZoom: 5000, // Load 5k properties at high zoom (viewport filtered)
		},
	};
	
	const useOptimizedData = true; // ENABLED: Use optimized viewport-based loading for 64k+ properties
	
	const legacyData = usePropertyData(organizationId, view);
	const optimizedData = useViewportPropertyData(organizationId, view, performanceConfig);
	
	const { 
		properties, 
		mappableProperties, 
		isLoading, 
		isFetching,
		clusters = [],
		updateViewport,
		currentZoom,
		meta = {}
	} = useOptimizedData ? {
		...optimizedData,
		isFetching: optimizedData.isLoading,
	} : {
		...legacyData,
		clusters: [],
		updateViewport: () => {},
		currentZoom: 10,
		meta: {},
	};

	const {
		results: autocompleteResults,
		isVisible: isAutocompleteVisible,
		setVisible: setAutocompleteVisible,
		handleAutocomplete: debouncedAutocomplete,
		clearResults: clearAutocompleteResults,
	} = useAutocomplete();

	const {
		searchProperty,
		clearSelection,
	} = usePropertySearch(mappableProperties);

	const {
		boundaries,
		isLoading: boundariesLoading,
		fetchAndAddBoundary,
		removeBoundary,
		updateBoundaryStatus,
		clearBoundaries,
	} = usePropertyBoundaries(map);

	// Initialize deck.gl overlay for advanced visualizations
	const deckglOverlay = useDeckGL(map, mappableProperties, clusters, selectedProperty, controlsState.show3D, currentZoom, performanceConfig);

	// Add table instance for DataTableProvider
	const columnHelper = createColumnHelper<PropertyType>();
	const table = useReactTable({
		data: properties as PropertyType[],
		columns: [
			columnHelper.accessor('name', {
				cell: info => info.getValue(),
			}),
		],
		getCoreRowModel: getCoreRowModel(),
		state: {
			columnFilters: [],
		},
	});

	// Handle map load and 3D buildings layer
	useEffect(() => {
		if (!map || !mapLoaded) return;

		// Get first label layer ID when map loads
		if (!firstLabelLayerId) {
			const labelLayerId = getFirstLabelLayerId(map.getStyle());
			setFirstLabelLayerId(labelLayerId);
		}

		// Add/remove 3D buildings layer based on 3D mode
		const hasBuildings3D = map.getLayer('3d-buildings');
		
		if (controlsState.show3D && !hasBuildings3D) {
			// Check if composite source exists, if not add it
			try {
				const style = map.getStyle();
				const hasCompositeSource = style.sources && style.sources.composite;
				
				if (!hasCompositeSource) {
					// Add composite source if it doesn't exist
					map.addSource('composite', {
						type: 'vector',
						url: 'mapbox://mapbox.mapbox-streets-v8'
					});
				}

				// Wait a bit for the source to be added, then add the layer
				setTimeout(() => {
					try {
						map.addLayer(buildings3DLayer, firstLabelLayerId);
						
						// Set dramatic 3D camera view
						map.flyTo({
							pitch: 60,
							bearing: 20,
							duration: 1500
						});
					} catch (layerError) {
						console.warn('Could not add 3D buildings layer:', layerError);
						// Fallback: just set the camera view without buildings
						map.flyTo({
							pitch: 60,
							bearing: 20,
							duration: 1500
						});
					}
				}, hasCompositeSource ? 0 : 500);

			} catch (error) {
				console.warn('Could not setup 3D buildings:', error);
				// Fallback: just set the camera view
				map.flyTo({
					pitch: 60,
					bearing: 20,
					duration: 1500
				});
			}
		} else if (!controlsState.show3D && hasBuildings3D) {
			// Remove 3D buildings layer and reset camera
			try {
				map.removeLayer('3d-buildings');
				
				// Reset to flat view
				map.flyTo({
					pitch: 0,
					bearing: 0,
					duration: 1500
				});
			} catch (error) {
				console.warn('Could not remove 3D buildings layer:', error);
				// Still reset camera even if layer removal fails
				map.flyTo({
					pitch: 0,
					bearing: 0,
					duration: 1500
				});
			}
		}
	}, [map, mapLoaded, controlsState.show3D, firstLabelLayerId]);

	// Handle viewport changes for progressive loading with clustering optimization
	useEffect(() => {
		if (!map || !mapLoaded || !useOptimizedData) return;

		let timeoutId: NodeJS.Timeout;

		const handleViewportChange = () => {
			// Debounce viewport changes for better performance
			clearTimeout(timeoutId);
			timeoutId = setTimeout(() => {
				const bounds = map.getBounds();
				const zoom = map.getZoom();
				
				if (bounds) {
					// Add buffer around viewport for smoother panning
					const buffer = performanceConfig.viewportBuffer;
					const latBuffer = (bounds.getNorth() - bounds.getSouth()) * buffer;
					const lngBuffer = (bounds.getEast() - bounds.getWest()) * buffer;
					
					const bufferedBounds = {
						north: bounds.getNorth() + latBuffer,
						south: bounds.getSouth() - latBuffer,
						east: bounds.getEast() + lngBuffer,
						west: bounds.getWest() - lngBuffer,
					};

					// Pass clustering flag to indicate if clustering should be enabled
					const shouldCluster = zoom <= performanceConfig.clusterThreshold;
					
					// TODO: Enhance updateViewport to accept full clustering options object
					// For now, pass boolean for clustering enable/disable
					updateViewport(bufferedBounds, zoom, shouldCluster);
				}
			}, performanceConfig.debounceMs);
		};

		// Initial load with clustering options
		handleViewportChange();

		// Listen for map events with passive listeners for better performance
		map.on('moveend', handleViewportChange);
		map.on('zoomend', handleViewportChange);

		return () => {
			clearTimeout(timeoutId);
			map.off('moveend', handleViewportChange);
			map.off('zoomend', handleViewportChange);
		};
	}, [map, mapLoaded, updateViewport, useOptimizedData, performanceConfig]);

	// Progressive Loading & Memory Management for 1M+ Records
	// 
	// This implementation provides multi-level progressive loading:
	//
	// 1. **Viewport-based Loading**: Only loads properties within the current viewport + buffer
	// 2. **Zoom-based Level of Detail**: 
	//    - Low zoom (< 10): Simplified rendering with small dots, clustering enabled
	//    - Medium zoom (10-14): Enhanced rendering with clustering 
	//    - High zoom (> 14): Full detail rendering with individual properties
	// 3. **Server-side Clustering**: Clusters are computed server-side and sent as aggregated data
	// 4. **Memory Management**: Tracks memory usage and provides warnings/recommendations
	// 5. **Debounced Loading**: Viewport changes are debounced to prevent excessive API calls
	// 6. **Buffer Zones**: Loads data slightly outside viewport for smoother panning
	//
	// This approach can efficiently handle 1M+ records by:
	// - Never loading more than ~50k properties at once
	// - Using WebGL rendering (deck.gl) for GPU acceleration
	// - Eliminating dual rendering systems (no legacy markers)
	// - Providing adaptive performance based on data density
	//
	useEffect(() => {
		if (!useOptimizedData) return;

		const totalProperties = properties.length;
		const memoryUsagePercent = (totalProperties / performanceConfig.maxPropertiesInMemory) * 100;

		// Log performance metrics for monitoring
		console.log('Map Performance Stats:', {
			properties: totalProperties,
			clusters: clusters.length,
			memoryUsage: `${memoryUsagePercent.toFixed(1)}%`,
			zoom: currentZoom,
			clustered: meta.clustered,
			renderingMode: currentZoom < performanceConfig.simplificationZoom ? 'simplified' : 'detailed',
		});

		// Warn about performance issues
		if (totalProperties > performanceConfig.maxPropertiesInMemory * 0.8) {
			console.warn(`High memory usage: ${totalProperties} properties loaded (${memoryUsagePercent.toFixed(1)}%)`);
		}

		// Clean up old markers and deck.gl layers on unmount
		return () => {
			// Cleanup is handled by individual hooks
		};
	}, [properties.length, clusters.length, currentZoom, meta.clustered, useOptimizedData, performanceConfig]);

	// Performance monitoring
	const performanceMetrics = useMapPerformanceMonitor(
		properties,
		clusters,
		isLoading,
		currentZoom,
		map?.getBounds()
	);

	const optimizationRecommendations = useOptimizationRecommendations(
		properties,
		clusters,
		currentZoom,
		performanceMetrics
	);

	const handlePropertySelect = useCallback((property: SelectedPropertyType | null) => {
		if (!property) {
			setSelectedProperty(null);
			if (controlsState.showBoundaries) {
				// Clear selection state from all boundaries
				Array.from(boundaries.keys()).forEach(propertyId => {
					const boundary = boundaries.get(propertyId);
					if (boundary) {
						updateBoundaryStatus(propertyId, false, false);
					}
				});
			}
			return;
		}

		const propertyAsSelected = {
			_id: property.id,
			source: "database",
			isSelected: true,
			location: property.location,
			organizationId: organizationId, // Use organizationId from MapView props
			name: property.name,
			recordType: "property",
			isDeleted: property.isDeleted,
			createdAt: new Date(property.createdAt),
			updatedAt: new Date(property.updatedAt),
			createdBy: property.createdBy,
			updatedBy: property.updatedBy,
			status: property.status,
			unitMixes: property.unitMixes,
			saleHistory: property.saleHistory,
			foreclosureInfo: [] as any,
			propertyInfo: property.propertyInfo || {},
			propertyType: property.propertyType,
			mlsHistory: property.mlsHistory,
			mortgages: property.mortgages,
		};

		setSelectedProperty(propertyAsSelected as any);

		// Auto-zoom to property and enable 3D mode for better visualization
		if (map && propertyAsSelected.location) {
			const location = propertyAsSelected.location as any;
			const coordinates = location?.coordinates || location?.location?.coordinates;
			if (coordinates && Array.isArray(coordinates) && coordinates.length === 2) {
				const [lng, lat] = coordinates;
				
				// Enable 3D mode automatically when selecting property
				if (!controlsState.show3D) {
					updateControls({ show3D: true });
				}
				
				// Zoom to property with dramatic 3D camera angle
				map.flyTo({
					center: [lng, lat],
					zoom: 18, // Close zoom for detailed view
					pitch: 60, // Dramatic 3D angle
					bearing: 20, // Slight rotation for better perspective
					duration: 2000, // Smooth 2-second animation
					essential: true
				});
				
			}
		}

		// Update boundary if boundaries are enabled
		if (controlsState.showBoundaries) {
			const location = propertyAsSelected.location as any;
			const coordinates = location?.coordinates || location?.location?.coordinates;
			if (coordinates && Array.isArray(coordinates) && coordinates.length === 2) {
				const [lng, lat] = coordinates;
				
				// Extract address for boundary API
				const address = location?.address?.street && location?.address?.city ? 
					`${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.zip}` : 
					null;
				
				const payload: any = { lat, lng };
				
				if (address) {
					payload.address = address;
				} else {
					// Try to get REA ID from legalInfo
					const reaId = (property as any).legalInfo?.reaId;
					if (reaId) {
						const numericId = Number.parseInt(reaId);
						if (!isNaN(numericId)) {
							payload.id = numericId;
						}
					}
				}
				
				// Only fetch boundary if we have either address or valid ID
				if (payload.address || payload.id) {
					fetchAndAddBoundary(
						payload,
						propertyAsSelected._id,
						false, // isPinned
						true, // isSelected
					).catch(error => {
						console.error("Error handling property selection boundary:", error);
					});
				} else {
					console.warn("Cannot fetch boundary: missing address and valid ID", {
						propertyId: propertyAsSelected._id,
						coordinates,
						address,
						reaId: (property as any).legalInfo?.reaId
					});
				}
			}
		}
	}, [controlsState.showBoundaries, controlsState.show3D, boundaries, updateBoundaryStatus, setSelectedProperty, fetchAndAddBoundary, organizationId, map, updateControls]);

	// Set up global callback for deck.gl property selection
	useEffect(() => {
		if (typeof window !== 'undefined') {
			(window as any).__deckgl_property_select = (property: any) => {
				handlePropertySelect({ ...property, propertyInfo: {} } as SelectedPropertyType);
			};
		}
		
		return () => {
			if (typeof window !== 'undefined') {
				delete (window as any).__deckgl_property_select;
			}
		};
	}, [handlePropertySelect]);

	const handlePropertyPin = useCallback(
		async (property: any, isPinned: boolean) => {
			if (!controlsState.showBoundaries) return;

			const propertyId = property._id || property.id;
			const location = property.location as any;
			const coordinates = location?.coordinates || location?.location?.coordinates;
			if (propertyId && Array.isArray(coordinates) && coordinates.length === 2) {
				const [lng, lat] = coordinates;
				try {
					if (isPinned) {
						// Extract address for boundary API
						const address = location?.address?.street && location?.address?.city ? 
							`${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.zip}` : 
							null;
						
						const payload: any = { lat, lng };
						
						if (address) {
							payload.address = address;
						} else {
							// Try to get REA ID from legalInfo
							const reaId = (property as any).legalInfo?.reaId;
							if (reaId) {
								const numericId = Number.parseInt(reaId);
								if (!isNaN(numericId)) {
									payload.id = numericId;
								}
							}
						}
						
						// Only fetch boundary if we have either address or valid ID
						if (payload.address || payload.id) {
							await fetchAndAddBoundary(
								payload,
								propertyId,
								true, // isPinned
								true, // isSelected
							);
							// Update the selected property's pin state
							if (selectedProperty?.id === propertyId) {
								setSelectedProperty(prev => prev ? { ...prev, isPinned: true } : null);
							}
						} else {
							console.warn("Cannot pin boundary: missing address and valid ID", {
								propertyId,
								coordinates,
								address,
								reaId: (property as any).legalInfo?.reaId
							});
						}
					} else {
						// If unpinning, also deselect and close sidebar
						removeBoundary(propertyId);
						if (selectedProperty?.id === propertyId) {
							setSelectedProperty(null);
						}
					}
				} catch (error) {
					console.error(
						"Error handling property pin boundary:",
						error,
					);
				}
			}
		},
		[fetchAndAddBoundary, removeBoundary, controlsState.showBoundaries, selectedProperty, setSelectedProperty],
	);

	// Remove legacy markers - use only deck.gl for performance with 1M+ records
	// All property visualization is now handled by the deck.gl overlay above
	// This eliminates dual rendering and significantly improves performance

	useEffect(() => {
		if (!controlsState.showBoundaries) {
			clearBoundaries();
		} else if (controlsState.showBoundaries && selectedProperty) {
			const propertyId =
				(selectedProperty as any)._id ||
				selectedProperty.id ||
				"searched-property";
			
			// Handle both direct coordinates and nested location structure
			const location = (selectedProperty.location as any);
			const coordinates =
				(selectedProperty as any).coordinates ||
				location?.coordinates ||
				location?.location?.coordinates ||
				(selectedProperty as any).propertyInfo?.address?.coordinates;

			if (
				coordinates &&
				Array.isArray(coordinates) &&
				coordinates.length === 2
			) {
				const [lng, lat] = coordinates;
				
				// Extract address from the property data structure
				const address =
					(selectedProperty as any).searchAddress ||
					(selectedProperty as any).propertyInfo?.address?.address ||
					location?.address?.street && location?.address?.city ? 
						`${location.address.street}, ${location.address.city}, ${location.address.state} ${location.address.zip}` : 
						null;

				// Ensure we have either an address or a valid ID for the REA API
				const payload: any = { lat, lng };
				
				if (address) {
					payload.address = address;
				} else {
					// Try to get REA ID from legalInfo or parse property ID
					const reaId = (selectedProperty as any).legalInfo?.reaId;
					if (reaId) {
						const numericId = Number.parseInt(reaId);
						if (!isNaN(numericId)) {
							payload.id = numericId;
						}
					} else if (selectedProperty.id) {
						// Fallback: try to parse the property ID as a number
						const numericId = Number.parseInt(selectedProperty.id);
						if (!isNaN(numericId)) {
							payload.id = numericId;
						}
					}
				}

				// Only make the API call if we have either address or valid ID
				if (payload.address || payload.id) {
					fetchAndAddBoundary(
						payload,
						propertyId,
						false, // isPinned
						true, // isSelected
					).catch((error) => {
						console.error("Error fetching boundary:", error);
					});
				} else {
					console.warn("Cannot fetch boundary: missing address and valid ID", {
						propertyId,
						coordinates,
						address,
						reaId: (selectedProperty as any).legalInfo?.reaId,
						location: location
					});
				}
			} else {
				console.error("Invalid coordinates for boundary fetch:", {
					coordinates,
					location,
					selectedProperty: selectedProperty.id
				});
			}
		}
	}, [
		controlsState.showBoundaries,
		clearBoundaries,
		selectedProperty,
		fetchAndAddBoundary,
	]);

	const handleSearch = useCallback(
		async (e: React.FormEvent) => {
			e.preventDefault();
			if (!controlsState.searchValue.trim() || controlsState.isSearching)
				return;

			updateControls({ isSearching: true });
			try {
				const result = await searchProperty(controlsState.searchValue);
				if (result && map) {
					// Slower, smoother animation with easing
					map.flyTo({
						center: result.coordinates as [number, number],
						zoom: 17,
						duration: 3000, // Increased from 1500ms to 3000ms for smoother animation
						essential: true, // Animation is considered essential for accessibility
					});
					
					// Set the selected property to trigger PIN and boundary display
					// Ensure coordinates are preserved from search result AND generate proper ID
					const propertyWithCoordinates = {
						...result.property,
						id: `search-${Date.now()}`,
						_id: `search-${Date.now()}`,
						coordinates: result.coordinates, // Preserve coordinates from search result
						location: result.property.location || {
							type: "Point",
							coordinates: result.coordinates
						}
					};
					handlePropertySelect(propertyWithCoordinates as any);
					
					// Automatically enable boundaries for searched properties to enhance UX
					if (!controlsState.showBoundaries) {
						updateControls({ showBoundaries: true });
					}
				}
			} catch (error) {
				console.error("Search error:", error);
			} finally {
				updateControls({ isSearching: false });
			}
		},
		[
			controlsState.searchValue,
			controlsState.isSearching,
			updateControls,
			searchProperty,
			map,
			handlePropertySelect,
		],
	);

	const handleSearchValueChange = useCallback(
		(value: string) => {
			updateControls({ searchValue: value });
			debouncedAutocomplete(value);
		},
		[updateControls, debouncedAutocomplete],
	);

	const handleAutocompleteSelect = useCallback(
		(result: AutocompleteResult) => {
			updateControls({ searchValue: result.address });
			setAutocompleteVisible(false);
			setTimeout(() => {
				const event = new Event("submit", {
					bubbles: true,
					cancelable: true,
				});
				const form = document.querySelector("form");
				if (form) form.dispatchEvent(event);
			}, 100);
		},
		[updateControls, setAutocompleteVisible],
	);

	const handleQuickViewClose = useCallback(() => {
		setSelectedProperty(null);
		handlePropertySelect(null);
		if (controlsState.showBoundaries) {
			// Clear selection state from all boundaries
			Array.from(boundaries.keys()).forEach(propertyId => {
				const boundary = boundaries.get(propertyId);
				if (boundary) {
					updateBoundaryStatus(propertyId, false, false);
				}
			});
		}
	}, [controlsState.showBoundaries, boundaries, updateBoundaryStatus, handlePropertySelect]);

	const isTableOpen = view?.mapConfig?.displayType === "table" || !view?.mapConfig?.displayType;
	const isGridOpen = view?.mapConfig?.displayType === "grid";

	return (
		<div className="h-full flex flex-col overflow-hidden">
			{renderHeader()}
			<div className="flex-1 relative min-h-0 overflow-hidden">
				{/* Map container */}
				<div
					ref={mapContainer}
					className="absolute inset-0 w-full h-full"
				/>

				{/* Map Controls */}
				<MapControls
					controlsState={controlsState}
					updateControls={updateControls}
					map={map}
					draw={draw}
					mappableProperties={mappableProperties}
					autocompleteResults={autocompleteResults}
					isAutocompleteVisible={isAutocompleteVisible}
					setAutocompleteVisible={setAutocompleteVisible}
					onSearch={handleSearch}
					onAutocompleteSelect={handleAutocompleteSelect}
					onSearchValueChange={handleSearchValueChange}
					className={cn(
						view?.mapConfig?.displayType === "grid" && "mr-[408px]"
					)}
				/>

				{/* Property overlays */}
				{view?.mapConfig?.displayType === "table" && (
					<div className="absolute bottom-0 left-0 right-0 z-20 bg-sidebar">
						<PropertyTable
							properties={properties}
							isOpen={true}
							onClose={() => {}}
							onPropertySelect={handlePropertySelect}
							view={view}
							organizationId={organizationId}
							selectedProperty={selectedProperty}
							isLoading={isLoading}
							isFetching={isFetching}
						/>
					</div>
				)}
				{view?.mapConfig?.displayType === "grid" && (
					<PropertyGrid
						properties={properties}
						isOpen={true}
						onClose={() => {}}
						onPropertySelect={handlePropertySelect as any}
						view={view}
						organizationId={organizationId}
						selectedProperty={selectedProperty}
						isLoading={isLoading}
						isFetching={isFetching}
					/>
				)}

				{/* Property sidebar */}
				{selectedProperty && (
					<PropertySidebar
						selectedProperty={selectedProperty}
						onClose={handleQuickViewClose}
						isTableOpen={view?.mapConfig?.displayType === "table"}
						onPinToggle={handlePropertyPin}
					/>
				)}

				{/* Progressive loading overlay with detailed status */}
				{(isLoading || isFetching) && (
					<div className="absolute top-4 left-4 bg-white/95 backdrop-blur rounded-lg shadow-lg px-4 py-3 z-10 min-w-[280px]">
						<div className="flex items-center gap-2 mb-2">
							<div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full" />
							<span className="text-sm font-medium text-gray-700">
								{useOptimizedData ? 'Loading viewport data...' : 'Loading properties...'}
							</span>
						</div>
						{useOptimizedData && (
							<div className="text-xs text-gray-500 space-y-1">
								<div className="flex justify-between">
									<span>🎯 Viewport loading:</span>
									<span className="text-green-600 font-medium">Active</span>
								</div>
								{meta.clustered && (
									<div className="flex justify-between">
										<span>🔗 Smart clustering:</span>
										<span className="text-blue-600 font-medium">Enabled</span>
									</div>
								)}
								<div className="flex justify-between">
									<span>⚡ Performance mode:</span>
									<span className="text-purple-600 font-medium">
										{currentZoom < performanceConfig.simplificationZoom ? 'Simplified' : 'Detailed'}
									</span>
								</div>
								<div className="flex justify-between">
									<span>🔍 Zoom level:</span>
									<span className="font-mono">{currentZoom.toFixed(1)}</span>
								</div>
								{meta.queryTime && (
									<div className="flex justify-between">
										<span>⏱️ Query time:</span>
										<span className="font-mono">{meta.queryTime}ms</span>
									</div>
								)}
							</div>
						)}
					</div>
				)}

				{/* Enhanced performance stats overlay with progressive loading details */}
				{!isLoading && (properties.length > 0 || clusters.length > 0) && (
					<div className="absolute top-4 right-4 bg-white/95 backdrop-blur rounded-lg shadow-lg px-4 py-3 z-10 min-w-[260px]">
						<div className="text-xs space-y-2">
							<div className="font-medium text-gray-700 border-b border-gray-200 pb-1">
								Performance Dashboard
							</div>
							<div className="space-y-1 text-gray-600">
								{/* Data Loading Metrics */}
								<div className="flex justify-between gap-3">
									<span>📍 Properties loaded:</span>
									<span className="font-mono text-green-600">{properties.length.toLocaleString()}</span>
								</div>
								{clusters.length > 0 && (
									<div className="flex justify-between gap-3">
										<span>🔗 Clusters active:</span>
										<span className="font-mono text-blue-600">{clusters.length.toLocaleString()}</span>
									</div>
								)}
								{meta.totalInViewport && (
									<div className="flex justify-between gap-3">
										<span>👁️ In viewport:</span>
										<span className="font-mono">{meta.totalInViewport.toLocaleString()}</span>
									</div>
								)}
								
								{/* Progressive Loading Status */}
								<div className="border-t border-gray-200 pt-1 mt-2"></div>
								<div className="flex justify-between gap-3">
									<span>🔍 Zoom level:</span>
									<span className="font-mono">{currentZoom.toFixed(1)}</span>
								</div>
								<div className="flex justify-between gap-3">
									<span>⚡ Render mode:</span>
									<span className={`font-mono ${currentZoom < performanceConfig.simplificationZoom ? 'text-blue-600' : 'text-green-600'}`}>
										{currentZoom < performanceConfig.simplificationZoom ? 'Simplified' : 'Detailed'}
									</span>
								</div>
								
								{/* Performance Metrics */}
								{meta.queryTime && (
									<div className="flex justify-between gap-3">
										<span>⏱️ Query time:</span>
										<span className="font-mono text-purple-600">{meta.queryTime}ms</span>
									</div>
								)}
								<div className="flex justify-between gap-3">
									<span>🎯 Engine:</span>
									<span className={`font-mono ${useOptimizedData ? 'text-green-600' : 'text-orange-600'}`}>
										{useOptimizedData ? 'Viewport+Deck.GL' : 'Legacy (Safe Mode)'}
									</span>
								</div>
								
								{/* Memory Usage Indicator */}
								{useOptimizedData && (
									<div className="flex justify-between gap-3">
										<span>💾 Memory:</span>
										<span className={`font-mono ${
											properties.length > performanceConfig.maxPropertiesInMemory * 0.8 
												? 'text-red-600' 
												: properties.length > performanceConfig.maxPropertiesInMemory * 0.6 
													? 'text-yellow-600' 
													: 'text-green-600'
										}`}>
											{((properties.length / performanceConfig.maxPropertiesInMemory) * 100).toFixed(1)}%
										</span>
									</div>
								)}
							</div>
						</div>
					</div>
				)}

				{/* Smart performance recommendations */}
				{(properties.length > 1000 || !useOptimizedData) && (
					<div className="absolute bottom-4 left-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg px-4 py-3 z-10 max-w-sm shadow-lg">
						<div className="text-sm text-blue-800">
							<div className="flex items-center gap-2 mb-2">
								<span>🚀</span>
								<strong>Optimization Active</strong>
							</div>
							<div className="text-xs space-y-1">
								{/* Progressive Loading Status */}
								<div className="text-green-700">
									✅ Viewport loading: {properties.length.toLocaleString()} properties loaded
								</div>
								
								{/* Clustering Status */}
								{clusters.length > 0 && (
									<div className="text-blue-700">
										✅ Smart clustering: {clusters.length} clusters active
									</div>
								)}
								
								{/* Performance Tips */}
								{currentZoom < 12 && (
									<div className="text-orange-700">
										💡 Zoom in to see individual properties
									</div>
								)}
								
								{properties.length > performanceConfig.maxPropertiesInMemory * 0.8 && (
									<div className="text-red-700">
										⚠️ High memory usage - consider filtering
									</div>
								)}
								
								{!controlsState.show3D && currentZoom < 14 && (
									<div className="text-purple-700">
										🎯 Try 3D mode for better clustering visualization
									</div>
								)}
								
								{/* Query Performance */}
								{meta.queryTime && meta.queryTime < 500 && (
									<div className="text-green-700">
										⚡ Fast query: {meta.queryTime}ms
									</div>
								)}
								
								{/* Memory Efficiency */}
								{useOptimizedData ? (
									<div className="text-green-700">
										💾 Memory efficient: WebGL rendering active
									</div>
								) : (
									<div className="text-orange-700">
										🛡️ Safe mode: Using legacy data loading for stability
									</div>
								)}
							</div>
						</div>
					</div>
				)}

				{/* Critical performance warning */}
				{properties.length > performanceConfig.maxPropertiesInMemory && (
					<div className="absolute bottom-4 right-4 bg-red-50 border border-red-300 rounded-lg px-4 py-3 z-10 max-w-sm">
						<div className="text-sm text-red-800">
							<div className="flex items-center gap-2 mb-2">
								<span>⚠️</span>
								<strong>High Memory Usage</strong>
							</div>
							<div className="text-xs space-y-1">
								<div>{properties.length.toLocaleString()} properties loaded</div>
								<div>Consider applying filters or zooming in</div>
								<div>Performance may be impacted</div>
							</div>
						</div>
					</div>
				)}

				{/* Boundaries loading overlay */}
				{boundariesLoading && (
					<div className="absolute top-4 left-4 bg-white rounded-lg shadow-md px-3 py-2 flex items-center gap-2 z-10">
						<div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full" />
						<span className="text-sm text-gray-600">
							Loading boundaries...
						</span>
					</div>
				)}

				{/* No location data message */}
				{!isLoading &&
					!isFetching &&
					properties.length > 0 &&
					mappableProperties.length === 0 && (
						<div className="absolute inset-0 flex items-center justify-center bg-gray-50">
							<div className="text-center p-8">
								<h3 className="text-lg font-medium text-gray-900 mb-2">
									No mappable properties
								</h3>
								<p className="text-gray-600">
									Properties need location coordinates to be
									displayed on the map.
								</p>
							</div>
						</div>
					)}
			</div>
		</div>
	);
}

export function MapView({
	objectType,
	organizationId,
	view,
	user,
	views = [],
	onViewChange,
	onRenameView,
	onDuplicateView,
	onDeleteView,
	onToggleViewFavorite,
	onToggleViewPublic,
	onSetViewDefault,
	onViewRenamed,
}: MapViewProps) {
	const handleCreateNew = () => {
		// TODO: Implement property creation
		console.warn("Create new property");
	};

	if (!organizationId) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<p className="text-muted-foreground">
						Organization not found...
					</p>
				</div>
			</div>
		);
	}

	const renderHeader = () => (
		<ObjectsHeader
			objectType={objectType}
			organizationId={organizationId}
			view={view}
			views={views}
			onViewChange={onViewChange}
			onViewRenamed={onViewRenamed}
			onUpdateView={onViewRenamed}
			user={user}
			handleCreateNew={handleCreateNew}
			primaryColumn={propertiesConfig.primaryColumn}
		/>
	);

	// Only render map for properties
	if (objectType !== "property") {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<p className="text-muted-foreground">
						Map view is only available for properties.
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full">
			<PropertiesMap
				organizationId={organizationId}
				renderHeader={renderHeader}
				view={view}
				onViewRenamed={onViewRenamed}
			/>
		</div>
	);
}
