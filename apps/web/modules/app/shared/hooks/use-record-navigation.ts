import { useSearchParams } from "next/navigation";
import { useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

export interface RecordNavigationInfo {
  currentIndex: number;
  totalRecords: number;
  filteredRecords: number;
  viewId?: string;
  viewName?: string;
  contextDescription?: string;
  hasPrevious: boolean;
  hasNext: boolean;
  previousId?: string;
  nextId?: string;
  navigateToPrevious: () => void;
  navigateToNext: () => void;
  isLoading: boolean;
  error?: any;
}

interface NavigationResponse {
  previousId: string | null;
  nextId: string | null;
  currentIndex: number;
  totalRecords: number;
  filteredRecords: number;
  hasFilters: boolean;
  contextDescription: string;
  viewName?: string;
}

export function useRecordNavigation(
  objectType: string,
  currentId: string,
  organizationId: string,
  // Optional context from the data table for better performance
  filterState?: Record<string, any>
): RecordNavigationInfo {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Build query parameters for the navigation API
  const queryParams = useMemo(() => {
    const params = new URLSearchParams();
    params.set('objectType', objectType);
    params.set('currentId', currentId);
    params.set('organizationId', organizationId);

    // If filterState is provided, use it directly (for performance)
    if (filterState) {
      Object.entries(filterState).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== "" && 
            !["size", "start", "sort", "id", "cursor", "direction", "live", "rowIndex"].includes(key)) {
          
          if (Array.isArray(value)) {
            params.set(key, value.join(","));
          } else {
            params.set(key, String(value));
          }
        }
      });
    } else {
      // Fallback to URL search params (excluding pagination and system params)
      const excludedParams = new Set(['cursor', 'direction', 'id', 'live', 'start', 'size', 'rowIndex']);

      searchParams.forEach((value, key) => {
        if (value && value.trim() && !excludedParams.has(key)) {
          params.set(key, value);
        }
      });
    }

    return params;
  }, [objectType, currentId, organizationId, filterState, searchParams]);

  // Fetch navigation data from API
  const { data: navigationData, isLoading, error } = useQuery<NavigationResponse>({
    queryKey: ["recordNavigation", objectType, currentId, organizationId, queryParams.toString()],
    queryFn: async () => {
      const url = `/api/navigation/record-context?${queryParams.toString()}`;

      const response = await fetch(url);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch record navigation data");
      }

      return response.json();
    },
    gcTime: 15 * 60 * 1000, // Keep in cache for 15 minutes
    staleTime: 10 * 60 * 1000, // Consider data fresh for 10 minutes
    enabled: !!currentId && !!organizationId,
    retry: (failureCount, error) => {
      // Don't retry if it's a 404 (record not found)
      if (error?.message?.includes("Record not found")) {
        return false;
      }
      return failureCount < 3;
    },
  });

  const hasPrevious = Boolean(navigationData?.previousId);
  const hasNext = Boolean(navigationData?.nextId);

  // Navigation functions
  const navigateToPrevious = useCallback(() => {
    if (!hasPrevious || !navigationData?.previousId) return;

    // Get the organization slug from the URL
    const orgSlug = window.location.pathname.split('/')[2];

    // Create new search params, preserving current filters
    const newParams = new URLSearchParams(searchParams.toString());
    
    // Remove navigation-specific params to avoid polluting the URL
    newParams.delete('rowIndex');
    newParams.delete('cursor');
    newParams.delete('direction');

    const paramString = newParams.toString();
    const url = `/app/${orgSlug}/${objectType}/${navigationData.previousId}${paramString ? `?${paramString}` : ''}`;
    router.push(url);
  }, [hasPrevious, navigationData, searchParams, router, objectType]);

  const navigateToNext = useCallback(() => {
    if (!hasNext || !navigationData?.nextId) return;

    // Get the organization slug from the URL
    const orgSlug = window.location.pathname.split('/')[2];

    // Create new search params, preserving current filters
    const newParams = new URLSearchParams(searchParams.toString());
    
    // Remove navigation-specific params to avoid polluting the URL
    newParams.delete('rowIndex');
    newParams.delete('cursor');
    newParams.delete('direction');

    const paramString = newParams.toString();
    const url = `/app/${orgSlug}/${objectType}/${navigationData.nextId}${paramString ? `?${paramString}` : ''}`;
    router.push(url);
  }, [hasNext, navigationData, searchParams, router, objectType]);

  // If there's an error, log it but don't crash
  if (error) {
    console.error("Record navigation error:", error);
  }

  return {
    currentIndex: navigationData?.currentIndex || 0,
    totalRecords: navigationData?.totalRecords || 0,
    filteredRecords: navigationData?.filteredRecords || 0,
    viewId: undefined,
    viewName: navigationData?.viewName,
    contextDescription: navigationData?.contextDescription,
    hasPrevious,
    hasNext,
    previousId: navigationData?.previousId || undefined,
    nextId: navigationData?.nextId || undefined,
    navigateToPrevious,
    navigateToNext,
    isLoading,
    error,
  };
} 