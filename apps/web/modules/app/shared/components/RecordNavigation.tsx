import { Button } from "@ui/components/button";
import { IconChevronUp, IconChevronDown } from "@tabler/icons-react";
import { useRecordNavigation } from "../hooks/use-record-navigation";
import { cn } from "@ui/lib";
import { useEffect, useState } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";

interface RecordNavigationProps {
  objectType: string;
  recordId: string;
  organizationId: string;
  className?: string;
  filterState?: Record<string, any>;
}

// Helper function to construct navigation URLs
function buildNavigationUrl(
  objectType: string, 
  targetId: string, 
  searchParams: URLSearchParams
): string {
  // Get the organization slug from the current URL
  const orgSlug = window.location.pathname.split('/')[2];
  
  // Create new search params, preserving current filters
  const newParams = new URLSearchParams(searchParams.toString());
  
  // Remove navigation-specific params to avoid polluting the URL
  newParams.delete('rowIndex');
  newParams.delete('cursor');
  newParams.delete('direction');

  const paramString = newParams.toString();
  return `/app/${orgSlug}/${objectType}/${targetId}${paramString ? `?${paramString}` : ''}`;
}

export function RecordNavigation({ 
  objectType, 
  recordId, 
  organizationId, 
  className, 
  filterState 
}: RecordNavigationProps) {
  const [showOptimistic, setShowOptimistic] = useState(true);
  const searchParams = useSearchParams();
  
  const {
    currentIndex,
    totalRecords,
    filteredRecords,
    contextDescription,
    hasPrevious,
    hasNext,
    previousId,
    nextId,
    navigateToPrevious,
    navigateToNext,
    isLoading,
    error,
  } = useRecordNavigation(objectType, recordId, organizationId, filterState);

  // Hide optimistic UI once real data loads
  useEffect(() => {
    if (!isLoading && (totalRecords > 0 || error)) {
      setShowOptimistic(false);
    }
  }, [isLoading, totalRecords, error]);

  // Add keyboard navigation
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      // Ignore if user is typing in an input or textarea
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      // Ignore if user is holding modifier keys
      if (event.ctrlKey || event.metaKey || event.altKey) {
        return;
      }

      switch (event.key.toLowerCase()) {
        case 'k':
          event.preventDefault();
          if (hasPrevious) {
            navigateToPrevious();
          }
          break;
        case 'j':
          event.preventDefault();
          if (hasNext) {
            navigateToNext();
          }
          break;
      }
    }

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [hasPrevious, hasNext, navigateToPrevious, navigateToNext]);

  // Build URLs for navigation
  const previousUrl = previousId 
    ? buildNavigationUrl(objectType, previousId, searchParams)
    : undefined;
  
  const nextUrl = nextId 
    ? buildNavigationUrl(objectType, nextId, searchParams)
    : undefined;

  // Show optimistic UI immediately to prevent blocking
  if (showOptimistic && isLoading) {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <div className="flex items-center gap-1 mr-2">
          <div className="flex flex-col items-center">
            <div className="text-sm font-medium text-foreground">
              <span className="font-mono">-</span>
              <span className="text-muted-foreground mx-1">of</span>
              <span className="font-mono">-</span>
              <span className="text-muted-foreground mx-1">in</span>
              <span className="text-muted-foreground truncate max-w-32 text-center">All</span>
            </div>
          </div>
        </div>
        <Button
          variant="relio"
          size="icon"
          className="h-8 w-8 p-0 text-muted-foreground opacity-50"
          disabled={true}
        >
          <IconChevronUp className="h-4 w-4" />
        </Button>
        
        <Button
          variant="relio"
          size="icon"
          className="h-8 w-8 p-0 text-muted-foreground opacity-50"
          disabled={true}
        >
          <IconChevronDown className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  // Handle error state gracefully
  if (error) {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <div className="flex items-center gap-1 mr-2">
          <div className="flex flex-col items-center">
            <div className="text-sm font-medium text-foreground">
              <span className="font-mono">-</span>
              <span className="text-muted-foreground mx-1">of</span>
              <span className="font-mono">-</span>
              <span className="text-muted-foreground mx-1">in</span>
              <span className="text-muted-foreground truncate max-w-32 text-center">All</span>
            </div>
          </div>
        </div>
        <Button
          variant="relio"
          size="icon"
          className="h-8 w-8 p-0 text-muted-foreground opacity-50"
          disabled={true}
        >
          <IconChevronUp className="h-4 w-4" />
        </Button>
        
        <Button
          variant="relio"
          size="icon"
          className="h-8 w-8 p-0 text-muted-foreground opacity-50"
          disabled={true}
        >
          <IconChevronDown className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  if (totalRecords === 0) {
    return null;
  }

  const position = currentIndex + 1;
  const total = filteredRecords;

  return (
    <div className={cn("flex items-center gap-1", className)}>
      <div className="flex items-center gap-1 mr-2">
        <div className="flex flex-col items-center">
          <div className="text-sm font-medium text-foreground">
            <span className="font-mono">{position}</span>
            <span className="text-muted-foreground mx-1">of</span>
            <span className="font-mono">{total.toLocaleString()}</span>
            <span className="text-muted-foreground mx-1">in</span>
            <span className="text-muted-foreground truncate max-w-32 text-center" title={contextDescription}>{contextDescription}</span>
          </div>
        </div>
      </div>
      
      {/* Previous Navigation */}
      {hasPrevious && previousUrl ? (
        <Link 
          href={previousUrl}
          className="inline-flex items-center justify-center h-8 w-8 p-0 text-muted-foreground transition-colors rounded-md border border-input hover:bg-accent hover:text-accent-foreground"
          title="Previous record (k)"
        >
          <IconChevronUp className="h-4 w-4" />
        </Link>
      ) : (
        <Button
          variant="relio"
          size="icon"
          className="h-8 w-8 p-0 text-muted-foreground opacity-50"
          disabled={true}
        >
          <IconChevronUp className="h-4 w-4" />
        </Button>
      )}
      
      {/* Next Navigation */}
      {hasNext && nextUrl ? (
        <Link 
          href={nextUrl}
          className="inline-flex items-center justify-center h-8 w-8 p-0 text-muted-foreground transition-colors rounded-md border border-input hover:bg-accent hover:text-accent-foreground"
          title="Next record (j)"
        >
          <IconChevronDown className="h-4 w-4" />
        </Link>
      ) : (
        <Button
          variant="relio"
          size="icon"
          className="h-8 w-8 p-0 text-muted-foreground opacity-50"
          disabled={true}
        >
          <IconChevronDown className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
} 