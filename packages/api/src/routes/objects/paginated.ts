import { <PERSON>o } from "hono";
import { zValida<PERSON> } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@repo/database/server";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import { logger } from "@repo/logs";
import { OBJECT_CONFIG, type ObjectType, normalizeObjectType } from "./config";
import { FIELD_MAPPINGS, JSON_FIELDS } from "@repo/database";

// Middleware to validate object type
const validateObjectTypeMiddleware = async (c: any, next: any) => {
	const objectTypeParam = c.req.param("objectType");
	const objectType = normalizeObjectType(objectTypeParam);

	if (!objectType) {
		return c.json({ error: "Invalid object type" }, 400);
	}

	c.set("objectType" as any, objectType);
	await next();
};

const objectsPaginatedRouter = new Hono();

// Helper function to get database table
function getDbTable(objectType: ObjectType) {
	switch (objectType) {
		case "contact":
			return db.contact;
		case "property":
			return db.property;
		case "company":
			return db.company;
		default:
			throw new Error(`Unknown object type: ${objectType}`);
	}
}

// Helper to build nested relation filters
function buildNestedFilter(fieldPath: string, value: string): any {
	const pathParts = fieldPath.split('.');
	
	if (pathParts.length === 2) {
		const [relation, field] = pathParts;
		
		// Handle range values (e.g., "20,200" or "20:200")
		if (value.includes(',') || value.includes(':')) {
			const separator = value.includes(',') ? ',' : ':';
			const [min, max] = value.split(separator).map(v => v.trim());
			const minNum = Number.parseFloat(min);
			const maxNum = Number.parseFloat(max);
			
			if (!Number.isNaN(minNum) && !Number.isNaN(maxNum)) {
				return {
					[relation]: {
						[field]: {
							gte: minNum,
							lte: maxNum,
						}
					}
				};
			}
		}
		
		// Handle single number values  
		const numValue = Number.parseFloat(value);
		if (!Number.isNaN(numValue)) {
			return {
				[relation]: {
					[field]: numValue
				}
			};
		}
		
		// Handle text values
		return {
			[relation]: {
				[field]: {
					contains: value,
					mode: "insensitive"
				}
			}
		};
	}
	
	return null;
}

// Paginated query endpoint with completely dynamic filtering
objectsPaginatedRouter.get(
	"/objects/:objectType/paginated",
	validateObjectTypeMiddleware,
	authMiddleware,
	async (c) => {
		try {
			const user = c.get("user");
			const objectType = c.get("objectType" as any) as ObjectType;

			// Parse query parameters
			const organizationId = c.req.query("organizationId");
			const query = c.req.query();

			if (!organizationId) {
				return c.json({ error: "Organization ID is required" }, 400);
			}

			await verifyOrganizationMembership(organizationId, user.id);

			const config = OBJECT_CONFIG[objectType];
			const table = getDbTable(objectType);

			// Process query parameters
			console.info(`Processing query parameters for ${objectType}:`, query);

			// Extract pagination parameters
			const limit = Math.min(parseInt(query.limit as string) || 100, 1000);
			const offset = parseInt(query.offset as string) || 0;

			// System parameters that should NOT be treated as database filters
			const systemParams = new Set([
				'organizationId', 'limit', 'offset', 'sort', 'id', 'cursor', 'direction', 
				'live', 'mapView', 'objectType', 'viewName', 'viewId'
			]);

			// Extract actual filter parameters (exclude system params)
			const filterParams: Record<string, any> = {};
			for (const [key, value] of Object.entries(query)) {
				if (!systemParams.has(key) && value !== null && value !== undefined && value !== "") {
					filterParams[key] = value;
				}
			}

			console.info(`Applied ${Object.keys(filterParams).length} dynamic filters for ${objectType}`);

			// Build where clause using centralized field mappings
			const where: any = {
				organizationId,
				isDeleted: false,
			};

			// Process dynamic filters using centralized mappings
			for (const [filterKey, filterValue] of Object.entries(filterParams)) {
				console.info(`Processing dynamic filter: ${filterKey} = ${filterValue}`);
				
				try {
					// Get the appropriate field mapping based on object type
					const fieldMapping = FIELD_MAPPINGS[objectType as keyof typeof FIELD_MAPPINGS];
					const dbField = fieldMapping?.[filterKey as keyof typeof fieldMapping] || filterKey;
					
					// Check if this is a JSON field by checking if dbField matches or starts with any JSON field prefix
					const isJsonField = Object.keys(JSON_FIELDS).some(jsonPrefix => 
						dbField === jsonPrefix || dbField.startsWith(jsonPrefix + '.')
					);
					
					// Handle different field types
					if (isJsonField) {
						// For direct JSON fields (contact/company email, phone, address, social)
						if (dbField in JSON_FIELDS) {
							// Handle direct JSON field search - use JSON contains
							where[dbField] = {
								string_contains: filterValue
							};
						} else {
							// Handle nested JSON fields (property location.address.city)
							const [relation, ...jsonPath] = dbField.split('.');
							if (relation && jsonPath.length > 0) {
								const jsonFieldPath = jsonPath.join('.');
								where[relation] = {
									[jsonFieldPath]: {
										path: jsonPath,
										string_contains: filterValue
									}
								};
							}
						}
					} else if (filterKey.includes('.')) {
						// Handle relation fields (physicalDetails.units, financials.price, etc.)
						const [relation, field] = filterKey.split('.');
						
						if (!where[relation]) {
							where[relation] = {};
						}
						
						// Check if it's a range filter (e.g., "20,200" or "20:200")
						if (typeof filterValue === 'string' && /^\d+[,:]\d+$/.test(filterValue)) {
							const [min, max] = filterValue.split(/[,:]/).map(v => parseInt(v.trim()));
							where[relation][field] = {
								gte: min,
								lte: max
							};
						} else {
							// Text search
							where[relation][field] = {
								contains: filterValue,
								mode: "insensitive"
							};
						}
					} else if (filterKey === 'name' && objectType === 'contact') {
						// Special handling for contact name (computed from firstName + lastName)
						where.OR = [
							{
								firstName: {
									contains: filterValue,
									mode: "insensitive"
								}
							},
							{
								lastName: {
									contains: filterValue,
									mode: "insensitive"
								}
							}
						];
					} else {
						// Handle direct fields
						if (Array.isArray(filterValue)) {
							where[dbField] = { in: filterValue };
						} else if (typeof filterValue === 'string' && /^\d+[,:]\d+$/.test(filterValue)) {
							// Range filter for direct numeric fields
							const [min, max] = filterValue.split(/[,:]/).map(v => parseInt(v.trim()));
							where[dbField] = {
								gte: min,
								lte: max
							};
						} else {
							// Text search with case insensitive matching
							where[dbField] = {
								contains: filterValue,
								mode: "insensitive"
							};
						}
					}
				} catch (error) {
					console.warn(`Failed to process filter ${filterKey}:`, error);
					// Skip invalid filters rather than failing the entire query
				}
			}

			// Add date range filtering
			if (query.createdAt) {
				const [start, end] = query.createdAt.split(":");
				if (start && end) {
					where.createdAt = {
						gte: new Date(Number.parseInt(start)),
						lte: new Date(Number.parseInt(end)),
					};
				}
			}

			// Add text search across relevant fields
			if (query.search) {
				const searchConditions = config.searchFields?.map((field) => ({
					[field]: { contains: query.search, mode: "insensitive" },
				})) || [];

				if (searchConditions.length > 0) {
					where.OR = searchConditions;
				}
			}

			const startTime = Date.now();

			// Execute queries in parallel for better performance
			const [totalCount, filteredData] = await Promise.all([
				// Get total count (unfiltered)
				table.count({
					where: {
						organizationId,
						isDeleted: false,
					},
				}),
				// Get filtered data with pagination
				table.findMany({
					where: where,
					include: config.include,
					orderBy: { createdAt: "desc" },
					take: limit,
					skip: offset,
				}),
			]);

			// Get filtered count if we have active filters
			const filteredCount = Object.keys(filterParams).length > 0 ? await table.count({
				where: where,
			}) : totalCount;

			const queryTime = Date.now() - startTime;

			logger.info(`Query completed in ${queryTime}ms for ${objectType}`);
			logger.info(`Paginated query for ${objectType} returned ${filteredData.length} of ${filteredCount} filtered results (${totalCount} total)`);

			return c.json({
				data: filteredData,
				pagination: {
					total: totalCount,
					filteredTotal: filteredCount, 
					limit,
					offset,
					hasMore: offset + limit < filteredCount,
				},
				meta: {
					totalRowCount: totalCount,
					filterRowCount: filteredCount,
				},
			});
		} catch (error) {
			logger.error("Failed to fetch paginated data:", error);
			return c.json({ error: "Failed to fetch data" }, 500);
		}
	},
);

export { objectsPaginatedRouter }; 