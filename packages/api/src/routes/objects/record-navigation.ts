import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import { OBJECT_CONFIG } from "./config";

export const recordNavigationRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// Define the query schema for record navigation - use a more flexible schema that accepts any filter
const RecordNavigationQuerySchema = z.object({
	objectType: z.enum(["contact", "property", "company"]),
	currentId: z.string().min(1),
	organizationId: z.string().min(1),
	// Optional filter parameters - accept any additional string parameters
	search: z.string().optional(),
	name: z.string().optional(),
	tags: z.string().optional(),
	propertyType: z.string().optional(),
	status: z.string().optional(),
	sort: z.string().optional(),
	// View context
	viewName: z.string().optional(),
	viewId: z.string().optional(),
}).passthrough(); // Allow additional properties

// Helper function to build context description
const buildContextDescription = (filters: any, viewName?: string) => {
	const contexts: string[] = [];
	const systemParams = ["objectType", "currentId", "organizationId", "viewName", "viewId", "sort"];
	
	// Process all filters dynamically
	Object.entries(filters).forEach(([key, value]) => {
		if (!value || typeof value !== 'string' || systemParams.includes(key)) return;
		
		// Handle special cases with custom formatting
		if (key === "search" || key === "name") {
			contexts.push(`"${value}"`);
		} else if (key === "tags") {
			const tagArray = value.split(",").filter((tag: string) => tag.trim() !== "");
			if (tagArray.length === 1) {
				contexts.push(`#${tagArray[0]}`);
			} else if (tagArray.length > 1) {
				contexts.push(`#${tagArray.join(", #")}`);
			}
		} else if (value.includes(",")) {
			// Handle array values (like propertyType, status arrays)
			const valueArray = value.split(",").filter((v: string) => v.trim() !== "");
			if (valueArray.length === 1) {
				contexts.push(`${valueArray[0]}`);
			} else if (valueArray.length > 1) {
				contexts.push(`${valueArray.join(" or ")}`);
			}
		} else {
			// Single value filters
			contexts.push(`${value}`);
		}
	});
	
	// If we have active filters, create a description
	if (contexts.length > 0) {
		// If there's a view name and filters, combine them
		if (viewName && viewName.trim() !== "" && viewName !== "All") {
			return `${viewName} • ${contexts.join(" • ")}`;
		}
		// Just filters without specific view
		return contexts.join(" • ");
	}
	
	// No active filters - just show view name or default
	if (viewName && viewName.trim() !== "") {
		// Clean up common view names
		const cleanViewName = viewName
			.replace(/\+/g, ' ')
			.replace(/View$/, '')
			.trim();
		return cleanViewName || "All";
	}
	
	// Fallback
	return "All";
};

// Get record navigation context
recordNavigationRouter.get(
	"/record-context",
	authMiddleware,
	validator("query", RecordNavigationQuerySchema),
	describeRoute({
		tags: ["Navigation"],
		summary: "Get record navigation context",
		description: "Get navigation context for a record including previous/next IDs and position within filtered results",
		responses: {
			200: {
				description: "Navigation context retrieved successfully",
				content: {
					"application/json": {
						schema: resolver(
							z.object({
								previousId: z.string().nullable(),
								nextId: z.string().nullable(),
								currentIndex: z.number(),
								totalRecords: z.number(),
								filteredRecords: z.number(),
								hasFilters: z.boolean(),
								contextDescription: z.string(),
								viewName: z.string().optional(),
							}),
						),
					},
				},
			},
			400: {
				description: "Invalid parameters",
				content: {
					"application/json": {
						schema: resolver(z.object({ error: z.string() })),
					},
				},
			},
			404: {
				description: "Record not found",
				content: {
					"application/json": {
						schema: resolver(z.object({ error: z.string() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const query = c.req.valid("query");
			const { objectType, currentId, organizationId, ...filters } = query;

			// Verify organization membership
			await verifyOrganizationMembership(organizationId, user.id);

			logger.info(`Record navigation request for ${objectType}:${currentId}`, {
				filters,
			});

			// Build filter conditions from query parameters
			const buildFilterConditions = (queryParams: typeof filters) => {
				const conditions: any = {
					organizationId,
					isDeleted: false,
				};

				const config = OBJECT_CONFIG[objectType];
				const systemParams = [
					"objectType", "currentId", "organizationId", "viewName", "viewId", "sort", 
					"cursor", "direction", "mapView", "live", "limit", "offset"
				];

				Object.entries(queryParams).forEach(([key, value]) => {
					if (!value || typeof value !== 'string' || systemParams.includes(key)) return;

					// Handle special cases first
					if (key === "search") {
						// Use the search fields from config
						const searchConditions = config.searchFields.map((field) => ({
							[field]: { contains: value, mode: "insensitive" }
						}));
						if (searchConditions.length > 0) {
							conditions.OR = searchConditions;
						}
						return;
					}

					if (key === "name" && objectType === "contact") {
						// For contacts, name filter searches in both firstName and lastName
						conditions.OR = [
							{ firstName: { contains: value, mode: "insensitive" } },
							{ lastName: { contains: value, mode: "insensitive" } },
						];
						return;
					}

					if (key === "tags") {
						const tagArray = value.split(",").filter(Boolean);
						if (tagArray.length > 0) {
							conditions.objectTags = {
								some: {
									tag: {
										name: { in: tagArray },
										organizationId,
									}
								}
							};
						}
						return;
					}

					// Handle other filters dynamically based on config
					const filterType = (config.filterFields as any)[key];
					if (filterType) {
						if (filterType === "string") {
							conditions[key] = {
								contains: value,
								mode: "insensitive",
							};
						} else if (filterType === "stringArray" || value.includes(",")) {
							const values = value.split(",").map((v: string) => v.trim()).filter(Boolean);
							if (values.length > 0) {
								conditions[key] = {
									in: values,
								};
							}
						} else if (filterType === "number") {
							try {
								if (value.includes(",")) {
									// Range filter like "100000,500000"
									const [min, max] = value.split(",").map(Number);
									if (!isNaN(min) || !isNaN(max)) {
										const numericFilter: any = {};
										if (!isNaN(min)) numericFilter.gte = min;
										if (!isNaN(max)) numericFilter.lte = max;
										conditions[key] = numericFilter;
									}
								} else {
									// Single value filter
									const numValue = Number(value);
									if (!isNaN(numValue)) {
										conditions[key] = numValue;
									}
								}
							} catch (error) {
								logger.warn(`Invalid numeric filter value for ${key}:`, value);
							}
						} else if (filterType === "relation" && key === "company" && objectType === "contact") {
							conditions.company = {
								name: { contains: value, mode: "insensitive" },
							};
						} else {
							// Default to exact match
							conditions[key] = value;
						}
					} else {
						// For filters not in config, try to handle them generically
						if (key.includes(".")) {
							// Handle nested fields dynamically
							const parts = key.split(".");
							const rootField = parts[0];
							const nestedField = parts.slice(1).join(".");
							
							// Special handling for property relations to optimize queries
							if (objectType === "property" && rootField === "address") {
								// For address fields, use the location relation
								if (!conditions.location) {
									conditions.location = {};
								}
								conditions.location.address = {
									...conditions.location.address,
									[nestedField]: { contains: value, mode: "insensitive" },
								};
							} else if (objectType === "property" && rootField === "physicalDetails") {
								// Try to handle numeric values
								let processedValue: any = value;
								if (value.includes(",")) {
									// Range filter like "100000,500000" or "20,200"
									const values = value.split(",").map((v: string) => v.trim());
									const numericValues = values.map(Number);
									if (numericValues.every(n => !isNaN(n))) {
										const [min, max] = numericValues;
										const numericFilter: any = {};
										if (!isNaN(min)) numericFilter.gte = min;
										if (!isNaN(max)) numericFilter.lte = max;
										processedValue = numericFilter;
									}
								} else {
									// Single value - try to parse as number
									const numValue = Number(value);
									if (!isNaN(numValue)) {
										processedValue = numValue;
									}
								}
								
								// Apply optimized nested filtering for physicalDetails
								if (!conditions.physicalDetails) {
									conditions.physicalDetails = {};
								}
								conditions.physicalDetails[nestedField] = processedValue;
							} else if (objectType === "property" && rootField === "financials") {
								// Handle financial fields like price
								let processedValue: any = value;
								if (value.includes(",")) {
									// Range filter like "100000,500000"
									const values = value.split(",").map((v: string) => v.trim());
									const numericValues = values.map(Number);
									if (numericValues.every(n => !isNaN(n))) {
										const [min, max] = numericValues;
										const numericFilter: any = {};
										if (!isNaN(min)) numericFilter.gte = min;
										if (!isNaN(max)) numericFilter.lte = max;
										processedValue = numericFilter;
									}
								} else {
									// Single value - try to parse as number
									const numValue = Number(value);
									if (!isNaN(numValue)) {
										processedValue = numValue;
									}
								}
								
								// Apply optimized nested filtering for financials
								if (!conditions.financials) {
									conditions.financials = {};
								}
								conditions.financials[nestedField] = processedValue;
							} else {
								// Generic nested field handling for other cases
								let processedValue: any = value;
								if (value.includes(",")) {
									// Range filter like "100000,500000" or "20,200"
									const values = value.split(",").map((v: string) => v.trim());
									const numericValues = values.map(Number);
									if (numericValues.every(n => !isNaN(n))) {
										const [min, max] = numericValues;
										const numericFilter: any = {};
										if (!isNaN(min)) numericFilter.gte = min;
										if (!isNaN(max)) numericFilter.lte = max;
										processedValue = numericFilter;
									}
								} else {
									// Single value - try to parse as number
									const numValue = Number(value);
									if (!isNaN(numValue)) {
										processedValue = numValue;
									}
								}
								
								// Apply generic nested filtering
								if (!conditions[rootField]) {
									conditions[rootField] = {};
								}
								conditions[rootField][nestedField] = processedValue;
							}
						} else {
							// Handle flat fields dynamically
							if (value.includes(",")) {
								// Range filter like "100000,500000"
								const values = value.split(",").map((v: string) => v.trim());
								const numericValues = values.map(Number);
								if (numericValues.every(n => !isNaN(n))) {
									const [min, max] = numericValues;
									const numericFilter: any = {};
									if (!isNaN(min)) numericFilter.gte = min;
									if (!isNaN(max)) numericFilter.lte = max;
									conditions[key] = numericFilter;
								} else {
									// Array of string values
									conditions[key] = { in: values };
								}
							} else {
								// Single value - try to parse as number
								const numValue = Number(value);
								if (!isNaN(numValue)) {
									conditions[key] = numValue;
								} else {
									// String value - use contains for partial matching
									conditions[key] = { contains: value, mode: "insensitive" };
								}
							}
						}
					}
				});

				return conditions;
			};

			// Parse sort parameter
			const getSortParam = (sortString?: string) => {
				if (!sortString) return null;
				
				try {
					return JSON.parse(sortString);
				} catch {
					return null;
				}
			};

			const filterConditions = buildFilterConditions(filters);
			const sortParam = getSortParam(query.sort);

			// Get counts and navigation records
			let totalCount: number;
			let filteredCount: number;
			let currentIndex: number = -1;
			let previousId: string | null = null;
			let nextId: string | null = null;

			// Optimized approach using parallel queries to reduce latency
			const parallelOptimization = async (model: any, sortField: string, isDescending: boolean) => {
				const sortConditions = [{ [sortField]: isDescending ? ("desc" as const) : ("asc" as const) }];
				
				// Execute counts in parallel
				const [totalCountResult, filteredCountResult, currentRecord] = await Promise.all([
					model.count({
						where: { organizationId, isDeleted: false },
					}),
					model.count({
						where: filterConditions,
					}),
					model.findUnique({
						where: { id: currentId },
						select: { 
							id: true, 
							createdAt: true, 
							...(objectType === 'contact' ? { firstName: true, lastName: true } : { name: true }),
							...(sortParam?.id && { [sortParam.id]: true })
						},
					})
				]);

				if (!currentRecord) {
					return null;
				}

				const sortValue = currentRecord[sortField as keyof typeof currentRecord];

				// Build optimized conditions for before/after queries
				const beforeConditions = {
					...filterConditions,
					[sortField]: isDescending ? { gt: sortValue } : { lt: sortValue }
				};

				const afterConditions = {
					...filterConditions,
					[sortField]: isDescending ? { lt: sortValue } : { gt: sortValue }
				};

				// Execute navigation queries in parallel
				const [recordsBefore, prevRecord, nextRecord] = await Promise.all([
					model.count({ where: beforeConditions }),
					model.findFirst({
						where: beforeConditions,
						orderBy: isDescending ? [{ [sortField]: "asc" }] : sortConditions,
						select: { id: true },
					}),
					model.findFirst({
						where: afterConditions,
						orderBy: sortConditions,
						select: { id: true },
					})
				]);

				return {
					total_count: totalCountResult,
					filtered_count: filteredCountResult,
					current_index: recordsBefore,
					previous_id: prevRecord?.id || null,
					next_id: nextRecord?.id || null,
				};
			};

			// Handle different object types with optimized parallel queries
			switch (objectType) {
				case "contact": {
					const sortField = sortParam?.id || 'createdAt';
					const isDescending = sortParam?.desc !== false;
					
					const result = await parallelOptimization(db.contact, sortField, isDescending);
					
					if (!result) {
						return c.json({ error: "Record not found" }, 404);
					}
					
					totalCount = result.total_count;
					filteredCount = result.filtered_count;
					currentIndex = result.current_index;
					previousId = result.previous_id;
					nextId = result.next_id;
					break;
				}
				case "property": {
					const sortField = sortParam?.id || 'createdAt';
					const isDescending = sortParam?.desc !== false;
					
					const result = await parallelOptimization(db.property, sortField, isDescending);
					
					if (!result) {
						return c.json({ error: "Record not found" }, 404);
					}
					
					totalCount = result.total_count;
					filteredCount = result.filtered_count;
					currentIndex = result.current_index;
					previousId = result.previous_id;
					nextId = result.next_id;
					break;
				}
				case "company": {
					const sortField = sortParam?.id || 'createdAt';
					const isDescending = sortParam?.desc !== false;
					
					const result = await parallelOptimization(db.company, sortField, isDescending);
					
					if (!result) {
						return c.json({ error: "Record not found" }, 404);
					}
					
					totalCount = result.total_count;
					filteredCount = result.filtered_count;
					currentIndex = result.current_index;
					previousId = result.previous_id;
					nextId = result.next_id;
					break;
				}
				default:
					return c.json({ error: `Unsupported object type: ${objectType}` }, 400);
			}

			// Build context description
			const contextDescription = buildContextDescription(filters, query.viewName);

			logger.info(`Record navigation context: ${objectType}:${currentId} at position ${currentIndex + 1}/${filteredCount}`);

			return c.json({
				previousId,
				nextId,
				currentIndex, // This is the 0-based position within filtered results
				totalRecords: totalCount,
				filteredRecords: filteredCount,
				hasFilters: filteredCount !== totalCount,
				contextDescription,
				viewName: query.viewName,
			});

		} catch (error) {
			logger.error("Error in record navigation:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

export type RecordNavigationRouter = typeof recordNavigationRouter; 