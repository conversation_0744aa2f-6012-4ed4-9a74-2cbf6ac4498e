import { Hono } from "hono";
import { z } from "zod";
import { db } from "@repo/database/server";
import { authMiddleware } from "../../middleware/auth";
import { logger } from "@repo/logs";
import { verifyOrganizationMembership } from "../organizations/lib/membership";

export const propertiesRouter = new Hono();

// Viewport query schema for map-based requests
const ViewportQuerySchema = z.object({
	organizationId: z.string().min(1),
	north: z.number(),
	south: z.number(),
	east: z.number(),
	west: z.number(),
	zoom: z.number().min(1).max(22),
	enableClustering: z.boolean().optional().default(true),
	limit: z.number().int().positive().max(50000).optional().default(10000),
	clusterOptions: z.object({
		radius: z.number().optional(),
		maxZoom: z.number().optional(),
		minPoints: z.number().optional(),
		extent: z.number().optional(),
	}).optional(),
});

// Cluster result type
interface ClusterResult {
	id: string;
	position: [number, number]; // [lng, lat]
	count: number;
	averagePrice: number | null;
	bounds: {
		north: number;
		south: number;
		east: number;
		west: number;
	};
	properties?: string[]; // Property IDs in cluster
}

// Helper function to calculate distance between two points (Haversine formula)
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
	const R = 6371; // Earth's radius in kilometers
	const dLat = (lat2 - lat1) * Math.PI / 180;
	const dLon = (lon2 - lon1) * Math.PI / 180;
	const a = 
		Math.sin(dLat/2) * Math.sin(dLat/2) +
		Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
		Math.sin(dLon/2) * Math.sin(dLon/2);
	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
	return R * c;
}

		// Simple clustering algorithm optimized for map display
function clusterProperties(properties: any[], zoom: number, options?: {
	radius?: number;
	maxZoom?: number;
	minPoints?: number;
	extent?: number;
}): ClusterResult[] {
	if (zoom >= 14) {
		// At high zoom levels, don't cluster - return individual properties
		return properties.map(prop => {
			const locationData = prop.location?.location as any;
			const coordinates = locationData?.coordinates || [0, 0];
			
			return {
				id: `property-${prop.id}`,
				position: [coordinates[0], coordinates[1]],
				count: 1,
				averagePrice: prop.financials?.price || null,
				bounds: {
					north: coordinates[1],
					south: coordinates[1],
					east: coordinates[0],
					west: coordinates[0],
				},
				properties: [prop.id],
			};
		});
	}

	// Determine cluster radius based on zoom level
	const clusterRadius = options?.radius || Math.max(0.001, 0.1 / Math.pow(2, zoom - 8)); // Adaptive radius
	const clusters: ClusterResult[] = [];
	const processed = new Set<string>();

	for (const property of properties) {
		if (processed.has(property.id)) continue;

		const locationData = property.location?.location as any;
		const coordinates = locationData?.coordinates || [0, 0];

		const cluster: ClusterResult = {
			id: `cluster-${property.id}`,
			position: [coordinates[0], coordinates[1]],
			count: 1,
			averagePrice: property.financials?.price || 0,
			bounds: {
				north: coordinates[1],
				south: coordinates[1],
				east: coordinates[0],
				west: coordinates[0],
			},
			properties: [property.id],
		};

		processed.add(property.id);
		let totalPrice = property.financials?.price || 0;
		let priceCount = totalPrice > 0 ? 1 : 0;

		// Find nearby properties to cluster
		for (const otherProperty of properties) {
			if (processed.has(otherProperty.id)) continue;

			const otherLocationData = otherProperty.location?.location as any;
			const otherCoordinates = otherLocationData?.coordinates || [0, 0];

			const distance = calculateDistance(
				coordinates[1], coordinates[0],
				otherCoordinates[1], otherCoordinates[0]
			);

			if (distance <= clusterRadius) {
				processed.add(otherProperty.id);
				cluster.count++;
				cluster.properties!.push(otherProperty.id);

				// Update cluster bounds
				cluster.bounds.north = Math.max(cluster.bounds.north, otherCoordinates[1]);
				cluster.bounds.south = Math.min(cluster.bounds.south, otherCoordinates[1]);
				cluster.bounds.east = Math.max(cluster.bounds.east, otherCoordinates[0]);
				cluster.bounds.west = Math.min(cluster.bounds.west, otherCoordinates[0]);

				// Update average price
				if (otherProperty.financials?.price) {
					totalPrice += otherProperty.financials.price;
					priceCount++;
				}
			}
		}

		// Calculate average price
		cluster.averagePrice = priceCount > 0 ? totalPrice / priceCount : null;

		// Update cluster position to be center of bounds
		cluster.position = [
			(cluster.bounds.east + cluster.bounds.west) / 2,
			(cluster.bounds.north + cluster.bounds.south) / 2
		];

		clusters.push(cluster);
	}

	return clusters;
}

// Viewport-based property querying with clustering - Enhanced for 64k+ Properties
propertiesRouter.post("/viewport", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const body = await c.req.json();
		
		const {
			organizationId,
			north,
			south,
			east,
			west,
			zoom,
			enableClustering,
			limit,
			clusterOptions
		} = ViewportQuerySchema.parse(body);

		await verifyOrganizationMembership(organizationId, user.id);

		const startTime = Date.now();

		// More reasonable viewport area limits for 64k+ properties
		const viewportArea = Math.abs(north - south) * Math.abs(east - west);
		const maxAllowedArea = zoom < 8 ? 10000 : zoom < 10 ? 5000 : zoom < 12 ? 1000 : 100;
		
		if (viewportArea > maxAllowedArea) {
			logger.warn(`Viewport too large (${viewportArea.toFixed(2)}) for zoom ${zoom}, using clustering fallback`);
			// Instead of returning empty, use aggressive clustering
			const safeLimit = Math.min(1000, limit || 1000);
			const properties = await db.property.findMany({
				where: {
					organizationId,
					isDeleted: false,
					location: { isNot: null },
				},
				include: {
					location: { select: { location: true } },
					financials: { select: { price: true, estimatedValue: true } },
				},
				take: safeLimit,
				orderBy: { updatedAt: 'desc' }, // Get most recent properties
			});

			// Apply aggressive clustering for large viewport areas
			const clusters = clusterProperties(properties, Math.max(zoom - 2, 4)); // Use lower zoom for more aggressive clustering
			
			return c.json({
				properties: [],
				clusters,
				meta: {
					zoom,
					clustered: true,
					queryTime: Date.now() - startTime,
					bounds: { north, south, east, west },
					totalInViewport: properties.length,
					clusterCount: clusters.length,
					message: `Large viewport - showing ${clusters.length} clusters from ${properties.length} properties`
				},
			});
		}

		// Enhanced limits for 64k+ properties based on zoom level
		let effectiveLimit = limit || 10000;
		if (zoom < 8) {
			effectiveLimit = Math.min(effectiveLimit, 64000); // Full dataset at very low zoom
		} else if (zoom < 10) {
			effectiveLimit = Math.min(effectiveLimit, 30000); // Large subset for clustering
		} else if (zoom < 12) {
			effectiveLimit = Math.min(effectiveLimit, 10000); // Medium subset
		} else {
			effectiveLimit = Math.min(effectiveLimit, 5000); // Smaller subset for individual properties
		}

		logger.info(`Fetching properties: zoom=${zoom}, limit=${effectiveLimit}, clustering=${enableClustering}, area=${viewportArea.toFixed(4)}`);

		// Optimized query for large datasets
		const properties = await db.property.findMany({
			where: {
				organizationId,
				isDeleted: false,
				location: {
					isNot: null,
				},
			},
			include: {
				location: {
					select: {
						location: true,
					}
				},
				financials: {
					select: {
						price: true,
						estimatedValue: true,
					},
				},
				// Include minimal property details for map rendering
				// Don't include heavy relations like unitMixes, saleHistory, etc.
			},
			take: effectiveLimit,
			orderBy: [
				{ updatedAt: 'desc' }, // Most recent first
				{ createdAt: 'desc' }   // Then by creation date
			],
		});

		// Enhanced viewport filtering with spatial indexing simulation
		// TODO: Replace with proper spatial database queries once indexes are in place
		const viewportProperties = properties.filter(property => {
			if (!property.location?.location || typeof property.location.location !== 'object') return false;
			
			const locationData = property.location.location as any;
			if (!locationData.coordinates || !Array.isArray(locationData.coordinates)) return false;
			
			const [lng, lat] = locationData.coordinates;
			return typeof lng === 'number' && typeof lat === 'number' &&
				   !isNaN(lng) && !isNaN(lat) &&
				   lat <= north && lat >= south && lng <= east && lng >= west;
		});

		let result;
		let clustered = false;

		// Enhanced clustering logic for 64k+ properties
		if (enableClustering && (zoom < 14 || viewportProperties.length > 100)) {
			const clusterRadius = clusterOptions?.radius || 50;
			const clusterMaxZoom = clusterOptions?.maxZoom || 16;
			const clusterMinPoints = clusterOptions?.minPoints || 2;
			
			const clusters = clusterProperties(viewportProperties, zoom, {
				radius: clusterRadius,
				maxZoom: clusterMaxZoom,
				minPoints: clusterMinPoints,
				extent: clusterOptions?.extent || 512
			});
			
			// For high zoom levels, also include individual properties outside clusters
			const individualProperties = zoom >= 12 ? 
				viewportProperties
					.filter(prop => !isPropertyInAnyCluster(prop, clusters))
					.slice(0, 200) // Limit individual properties for performance
				: [];

			result = {
				clusters,
				properties: individualProperties,
				clustered: true,
				totalInViewport: viewportProperties.length,
				clusterCount: clusters.length,
				individualCount: individualProperties.length,
			};
			clustered = true;
		} else {
			// Return individual properties with enhanced data for map rendering
			const mapProperties = viewportProperties.slice(0, effectiveLimit).map(property => ({
				id: property.id,
				name: property.name,
				propertyType: property.propertyType,
				status: property.status,
				location: property.location?.location,
				price: property.financials?.price,
				estimatedValue: property.financials?.estimatedValue,
				// Add any other fields needed for map rendering
				createdAt: property.createdAt,
				updatedAt: property.updatedAt,
			}));

			result = {
				properties: mapProperties,
				clusters: [],
				clustered: false,
				totalInViewport: viewportProperties.length,
				displayedCount: mapProperties.length,
			};
		}

		const queryTime = Date.now() - startTime;
		
		logger.info(`Viewport query completed: ${queryTime}ms, ${result.properties?.length || 0} properties, ${result.clusters?.length || 0} clusters`);

		return c.json({
			...result,
			meta: {
				zoom,
				clustered,
				queryTime,
				bounds: { north, south, east, west },
				area: viewportArea,
				effectiveLimit,
				dataStrategy: clustered ? 'clustering' : 'individual',
				...result
			},
		});
	} catch (error) {
		logger.error("Viewport query failed:", error);
		
		// Enhanced error handling with helpful fallback
		return c.json({
			properties: [],
			clusters: [],
			meta: {
				zoom: 10,
				clustered: false,
				queryTime: 0,
				bounds: { north: 0, south: 0, east: 0, west: 0 },
				error: error instanceof Error ? error.message : "Database query failed",
				recommendation: "Try zooming in or reducing the viewport area"
			},
		});
	}
});

// Get property clusters for a specific zoom level (alternative endpoint)
propertiesRouter.get("/clusters", authMiddleware, async (c) => {
	try {
		const user = c.get("user");
		const organizationId = c.req.query("organizationId");
		const zoom = parseInt(c.req.query("zoom") || "10");
		const north = parseFloat(c.req.query("north") || "90");
		const south = parseFloat(c.req.query("south") || "-90");
		const east = parseFloat(c.req.query("east") || "180");
		const west = parseFloat(c.req.query("west") || "-180");

		if (!organizationId) {
			return c.json({ error: "Organization ID is required" }, 400);
		}

		await verifyOrganizationMembership(organizationId, user.id);

		// For zoom levels 12 and above, return individual properties
		if (zoom >= 12) {
			return c.json({
				clusters: [],
				useIndividualProperties: true,
				message: "Use individual property endpoint for high zoom levels"
			});
		}

		// Get properties in viewport
		const properties = await db.property.findMany({
			where: {
				organizationId,
				isDeleted: false,
				location: {
					isNot: null,
				},
			},
			include: {
				location: {
					select: {
						location: true,
					},
				},
				financials: {
					select: {
						price: true,
					},
				},
			},
			take: 50000, // Limit for performance
		});

		// Filter by bounds and cluster
		const viewportProperties = properties.filter(property => {
			if (!property.location?.location || typeof property.location.location !== 'object') return false;
			
			const locationData = property.location.location as any;
			if (!locationData.coordinates || !Array.isArray(locationData.coordinates)) return false;
			
			const [lng, lat] = locationData.coordinates;
			return lat <= north && lat >= south && lng <= east && lng >= west;
		});

		const clusters = clusterProperties(viewportProperties, zoom);

		return c.json({
			clusters,
			totalProperties: viewportProperties.length,
			zoom,
			bounds: { north, south, east, west },
		});

	} catch (error) {
		logger.error("Cluster query failed:", error);
		return c.json({ error: "Failed to fetch clusters" }, 500);
	}
}); 