export const filterTypes = ["text", "number", "boolean", "date", "array", "object", "email", "phone", "select", "multiselect", "input", "checkbox", "timerange", "slider", "readonly", "company"] as const;
export type FilterType = (typeof filterTypes)[number];

export const filterOperators = ["eq", "neq", "gt", "gte", "lt", "lte", "contains", "notContains", "startsWith", "endsWith", "is", "isNot"] as const;
export type FilterOperator = (typeof filterOperators)[number];

export const filterConditions = ["and", "or"] as const;
export type FilterCondition = (typeof filterConditions)[number];