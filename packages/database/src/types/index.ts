export * from "./field";
export * from "./filter";
export * from "./object";

export const CONTACT_FIELD_MAPPINGS = {
	// Simple fields that map directly to database columns
	firstName: "firstName",
	lastName: "lastName",
	name: "name", // Computed field from firstName + lastName
	title: "title",
	persona: "persona",
	status: "status",
	website: "website",
	source: "source",
	stage: "stage",
	birthday: "birthday",
	age: "age",
	spouseName: "spouseName",
	summary: "summary",
	createdAt: "createdAt",
	updatedAt: "updatedAt",
	
	// JSON fields - these are stored as JSON in the database
	email: "email",
	phone: "phone",
	address: "address",
	social: "social",
	
	// Relation fields
	company: "company",
	companyId: "companyId",
	
	// Special fields
	tags: "objectTags",
} as const;

export const COMPANY_FIELD_MAPPINGS = {
	// Simple fields that map directly to database columns
	name: "name",
	website: "website",
	industry: "industry",
	size: "size",
	description: "description",
	createdAt: "createdAt",
	updatedAt: "updatedAt",
	
	// JSON fields - these are stored as J<PERSON><PERSON> in the database
	address: "address",
	phone: "phone",
	email: "email",
	
	// Special fields
	tags: "objectTags",
} as const;

export const PROPERTY_FIELD_MAPPINGS = {
	// Simple fields that map directly to database columns
	name: "name",
	propertyType: "propertyType", 
	status: "status",
	description: "description",
	createdAt: "createdAt",
	updatedAt: "updatedAt",
	
	// Address fields - map to location.address JSON structure
	city: "location.address.city",
	state: "location.address.state",
	zip: "location.address.zip", 
	street: "location.address.street",
	county: "location.address.county",
	
	// Physical details - map to physicalDetails relation
	units: "physicalDetails.units",
	bedrooms: "physicalDetails.bedrooms",
	bathrooms: "physicalDetails.bathrooms",
	squareFootage: "physicalDetails.squareFootage",
	yearBuilt: "physicalDetails.yearBuilt",
	lotSize: "physicalDetails.lotSize",
	floors: "physicalDetails.floors",
	
	// Financial fields - map to financials relation
	price: "financials.price",
	estimatedValue: "financials.estimatedValue",
	equity: "financials.equity",
	
	// Special fields
	tags: "objectTags",
} as const;

// Combined field mappings by object type
export const FIELD_MAPPINGS = {
	contact: CONTACT_FIELD_MAPPINGS,
	company: COMPANY_FIELD_MAPPINGS,
	property: PROPERTY_FIELD_MAPPINGS,
} as const;

export const REVERSE_PROPERTY_FIELD_MAPPINGS = Object.fromEntries(
	Object.entries(PROPERTY_FIELD_MAPPINGS).map(([key, value]) => [value, key])
) as Record<string, string>;

export const JSON_FIELDS = {
	// Property JSON fields
	"location.address": ["city", "state", "zip", "street", "county"],
	
	// Contact JSON fields - these are stored directly as JSON
	"address": ["street", "city", "state", "zip", "country"],
	"phone": ["number", "type", "isPrimary"],
	"email": ["address", "type", "isPrimary"],
	"social": ["linkedin", "facebook", "twitter", "instagram"],
} as const;

export const PROPERTY_FIELD_TYPES = {
	// Text fields
	name: "text",
	description: "textarea",
	street: "text",
	city: "text", 
	state: "text",
	zip: "text",
	county: "text",
	
	// Select fields
	propertyType: "select",
	status: "select",
	
	// Number fields  
	units: "number",
	bedrooms: "number",
	bathrooms: "number",
	squareFootage: "number",
	yearBuilt: "number",
	lotSize: "number",
	floors: "number",
	price: "number",
	estimatedValue: "number",
	equity: "number",
	
	// Special fields
	tags: "tags",
	createdAt: "date",
	updatedAt: "date",
} as const;

export const PROPERTY_FILTER_FIELDS = [
	{
		label: "Name",
		value: "name" as const,
		type: "text" as const,
	},
	{
		label: "City", 
		value: "city" as const,
		type: "text" as const,
	},
	{
		label: "State",
		value: "state" as const,
		type: "text" as const,
	},
	{
		label: "Units",
		value: "units" as const,
		type: "range" as const,
	},
	{
		label: "Bedrooms",
		value: "bedrooms" as const,
		type: "range" as const,
	},
	{
		label: "Bathrooms", 
		value: "bathrooms" as const,
		type: "range" as const,
	},
	{
		label: "Price",
		value: "price" as const,
		type: "range" as const,
	},
	{
		label: "Property Type",
		value: "propertyType" as const,
		type: "select" as const,
	},
	{
		label: "Status",
		value: "status" as const,
		type: "select" as const,
	},
] as const;

export type PropertyFilterField = typeof PROPERTY_FILTER_FIELDS[number]["value"];
export type PropertyFieldType = keyof typeof PROPERTY_FIELD_TYPES;