export const TAGGABLE_OBJECT_TYPES = [
	'contact',
	'company',
	'property',
	'custom_object',
] as const;

export type TaggableObjectType = typeof TAGGABLE_OBJECT_TYPES[number];

export type AuthContext = {
	organizationId: string;
	userId: string;
};

export const objectTypes = [
	"contact",
	"company",
	"property",
	"custom_object",
	"task",
	"note",
	"view"
] as const;
export type ObjectType = (typeof objectTypes)[number];

export const objectViewTypes = ["table", "kanban", "map"] as const;
export type ObjectViewType = (typeof objectViewTypes)[number];

export const objectStatusFields = ["status", "stage"] as const;
export type ObjectStatusField = (typeof objectStatusFields)[number];

// URL mapping for routing - converts between singular ObjectType and plural URL segments
export const pluralToSingularMap: Record<string, ObjectType> = {
	contacts: "contact",
	companies: "company", 
	properties: "property",
	// Keep singular forms for backward compatibility
	contact: "contact",
	company: "company",
	property: "property",
	custom_object: "custom_object",
	task: "task",
	note: "note",
	view: "view",
};

export const singularToPluralMap: Record<ObjectType, string> = {
	contact: "contacts",
	company: "companies",
	property: "properties",
	custom_object: "custom_objects",
	task: "tasks", 
	note: "notes",
	view: "views",
};